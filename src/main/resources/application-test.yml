app:
  baseUrl: http://*************:8100/
  datasource:
    host: *************:31656
    username: root
    password: Btzn@2022
    name: patrol_info_dispatch
#  rabbitmq:
#    host: *************
#    port: 5672
#    username: admin
#    password: Btzn@2022
  rabbitmq:
    host: **************
    port: 5672
    username: admin
    password: Botsmart@2021
  redis:
    host: *************
    database: 10
    port: 7011
    password: Btzn@2022

#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
customs:
  weibo:
    #轻博数据处理
    customer-rabbitmq-queue: queue_weibo_callBackTss_mi
    customer-rabbitmq-direct: direct_weibo_callBackTss_mi
    customer-rabbitmq-key: test_weibo_callBackTs_mi
  props:
    customer-rabbitmq-queue: queue_callBack_mi
    customer-rabbitmq-direct: direct_callBack_mi
    customer-rabbitmq-key: callBack_mi
    #发送数据异常处理
    customer-queue-fail-callback: queue_fail_callBack_mi
    customer-direct-fail-callback: direct_fail_callBack_mi
    customer-key-fail-callback: data_fail_callBack_mi
  patrol-data-center-url: http://*************:8100
  task:
    # 中台任务分发处理
    rabbitmq-task-dispatch-direct: direct_task_dispatch_mi
    rabbitmq-task-dispatch-queue: queue_task_dispatch_mi
    rabbitmq-task-dispatch-key: task_dispatch_mi
    #执行器任务控制 - 运行/停止
    rabbitmq-direct-task-control: direct_task_control_mi
    rabbitmq-key-task-control: task_control_mi
    #执行器任务控制接收后回调状态
    rabbitmq-direct-actuator-callback: direct_actuator_callback_mi
    rabbitmq-queue-actuator-callback: queue_actuator_callback_mi
    rabbitmq-key-actuator-callback: actuator_callback_mi
    #轻博数据处理
    rabbitmq-direct-callback: direct_task_callback_mi
    rabbitmq-queue-callback: queue_task_callback_mi
    rabbitmq-key-callback: key_task_callback_mi
    #回查任务处理-下发
    rabbitmq-direct-lookback: direct_xhw_lookback_mi
    rabbitmq-queue-lookback-webo: queue_xhw_lookback_webo_mi
    rabbitmq-queue-lookback-wechat: queue_xhw_lookback_wechat_mi
    #回查任务处理-回调结果
    rabbitmq-direct-lookback-result: direct_xhw_lookback_result_mi
    rabbitmq-queue-lookback-result-webo: queue_xhw_lookback_result_webo_mi
    rabbitmq-queue-lookback-result-wechat: queue_xhw_lookback_result_wechat_mi
    #回查任务处理-通用key
    rabbitmq-key-lookback-webo: xhw_lookback_webo_mi
    rabbitmq-key-lookback-wechat: xhw_lookback_wechat_mi
internalSystemAddress: https://pgc.botsmart.cn/
patUrl: http://**********:9201/
convertPdfUrl: http://************:9202/Screenshot
thirdPartyPlatformAddress: http://*************:10009/trsdc/
callbackKey: A10A2FFBE5BE4F3C9757E0CF6BABFEE9
qingboUrl: http://projects-databus.gsdata.cn:7777/
qingboProjectId: 10016
qingboSign: 5b8cd6cbe42181fa52f9dc236bf33331
days: -100
proxy:
  targetAddr: http://*************:8100
elasticsearch:
  host: *************
  port: 9200
  connTimeout: 3000
  socketTimeout: 5000
  connectionRequestTimeout: 500
  #  索引名称
  index-name: call-back-data
  username: elastic
  password: Btzn@2022
  max-conn-total: 20
  max-conn-per-route: 20
weiboAddress: http://**************:7001/
runscheduled: 0
lookbackScheduled: 0
weboscheduled: 0
