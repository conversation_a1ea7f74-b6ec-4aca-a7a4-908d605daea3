app:
  baseUrl: http://*********:8100/
  datasource:
    host: 8e131b904093490ab0b5908dd917a365in01.internal.cn-north-4.mysql.rds.myhuaweicloud.com:3306
    username: patrol_info
    password: <PERSON>ts<PERSON>@2022
    name: patrol_info_dispatch
  rabbitmq:
    host: ************
    port: 5672
    username: admin
    password: Botsmart@2021
  redis:
    host: redis-89541461-ea64-4df7-a1e7-529027a299e8.dcs.huaweicloud.com
    database: 10
    port: 6379
    password: Botsmart@2021

#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
customs:
  weibo:
    #轻博数据处理
    customer-rabbitmq-queue: queue_weibo_callBackTss
    customer-rabbitmq-direct: direct_weibo_callBackTss
    customer-rabbitmq-key: test_weibo_callBackTs
  props:
    customer-rabbitmq-queue: queue_callBack
    customer-rabbitmq-direct: direct_callBack
    customer-rabbitmq-key: callBack
    #发送数据异常处理
    customer-queue-fail-callback: queue_fail_callBack
    customer-direct-fail-callback: direct_fail_callBack
    customer-key-fail-callback: data_fail_callBack
  patrol-data-center-url: http://*********:8100
  task:
    # 中台任务分发处理
    rabbitmq-task-dispatch-direct: direct_task_dispatch
    rabbitmq-task-dispatch-queue: queue_task_dispatch
    rabbitmq-task-dispatch-key: task_dispatch
    #执行器任务控制 - 运行/停止
    rabbitmq-direct-task-control: direct_task_control
    rabbitmq-key-task-control: task_control
    #执行器任务控制接收后回调状态
    rabbitmq-direct-actuator-callback: direct_actuator_callback
    rabbitmq-queue-actuator-callback: queue_actuator_callback
    rabbitmq-key-actuator-callback: actuator_callback
    #轻博数据处理
    rabbitmq-direct-callback: direct_task_callback
    rabbitmq-queue-callback: queue_task_callback
    rabbitmq-key-callback: key_task_callback
    #回查任务处理-下发
    rabbitmq-direct-lookback: direct_xhw_lookback
    rabbitmq-queue-lookback-webo: queue_xhw_lookback_webo
    rabbitmq-queue-lookback-wechat: queue_xhw_lookback_wechat
    #回查任务处理-回调结果
    rabbitmq-direct-lookback-result: direct_xhw_lookback_result
    rabbitmq-queue-lookback-result-webo: queue_xhw_lookback_result_webo
    rabbitmq-queue-lookback-result-wechat: queue_xhw_lookback_result_wechat
    #回查任务处理-通用key
    rabbitmq-key-lookback-webo: xhw_lookback_webo
    rabbitmq-key-lookback-wechat: xhw_lookback_wechat
internalSystemAddress: https://pgc.botsmart.cn/
patUrl: http://**********:9201/
convertPdfUrl: http://************:9202/Screenshot
thirdPartyPlatformAddress: http://*************:10009/trsdc/
callbackKey: A10A2FFBE5BE4F3C9757E0CF6BABFEE9
qingboUrl: http://projects-databus.gsdata.cn:7777/
qingboProjectId: 10016
qingboSign: 5b8cd6cbe42181fa52f9dc236bf33331
days: -100
proxy:
  targetAddr: http://*********:8100
elasticsearch:
  host: *********,*********,*********
  port: 9200,9200,9200
  connTimeout: 3000
  socketTimeout: 5000
  connectionRequestTimeout: 500
  #  索引名称
  index-name: call-back-data
  username: elastic
  password: <EMAIL>
  max-conn-total: 20
  max-conn-per-route: 20
weiboAddress: http://**************:7001/
runscheduled: 1
lookbackScheduled: 1
weboscheduled: 1