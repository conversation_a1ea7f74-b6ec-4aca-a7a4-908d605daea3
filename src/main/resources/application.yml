spring:
#  quartz:
#    job-store-type: jdbc #持久化到数据库
#    properties:
#      org:
#        quartz:
#          datasource:
#            # 新版驱动从com.mysql.jdbc.Driver变更为com.mysql.cj.jdbc.Driver
#            driver-class-name: com.mysql.cj.jdbc.Driver
#            # 数据源需要添加时间标准和指定编码格式解决乱码 You must configure either the server or JDBC driver (via the serverTimezone configuration property) to use a more specifc time zone value if you want to utilize time zone support.
#            url: ******************************************************************************************************************
#            username: root
#            password: Botsmart@2
#          scheduler:
#            instancName: clusteredScheduler
#            instanceId: AUTO
#          jobStore:
#            class: org.quartz.impl.jdbcjobstore.JobStoreTX
#            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate #StdJDBCDelegate说明支持集群
#            tablePrefix: QRTZ_
#            isClustered: true
#            clusterCheckinInterval: 1000
#            useProperties: false
#          threadPool:
#            class: org.quartz.simpl.SimpleThreadPool
#            threadCount: 20
#            threadPriority: 5
#    overwrite-existing-jobs: false
  #  boot:
#    admin:
#      client:
#        instance:
#          metadata:
#            # 这里是我们在 client 设置安全账户信息  步骤中设置的账密
#            user.name: ${spring.security.user.name}
#            user.password: ${spring.security.user.password}
#  security:
#    user:
#      name: SBA_admin
#      password: SBA_password
#      roles: SBA_ADMIN
  profiles:
    active: @profile.active@
  application:
    name: patrol-info-dispatch
  servlet:
    multipart:
      enabled: true
      maxFileSize: 2048MB
      maxRequestSize: 2048MB
  datasource:
    name: ${app.datasource.name}
    url: jdbc:mysql://${app.datasource.host}/${app.datasource.name}?allowMultiQueries=true&useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${app.datasource.username}
    password: ${app.datasource.password}
    hikari:
      connectionTimeout: 60000
      maximumPoolSize: 20
      validation-timeout: 30000
      max-lifetime: 60000
      minimum-idle: 10
      idle-timeout: 60000
  rabbitmq:
    host: ${app.rabbitmq.host}
    port: ${app.rabbitmq.port}
    username: ${app.rabbitmq.username}
    password: ${app.rabbitmq.password}
    listener:
      direct:
        acknowledge-mode: manual
        prefetch: 1
        concurrency: 5 #消费者最小数量
        max-concurrency: 20
  #redis配置
  redis:
    database: ${app.redis.database} # Redis数据库索引（默认为0）
    host: ${app.redis.host} # Redis服务器地址
    port: ${app.redis.port} # Redis服务器连接端口
    password: ${app.redis.password} # Redis服务器连接密码（默认为空)
    timeout: 3000ms # 链接超时时间 单位 ms（毫秒）
    lettuce:
      pool:
        max-active: 8 # 连接池最大连接数（使用负值表示没有限制） 默认 8
        max-wait: -1ms # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
        max-idle: 8 # 连接池最大连接数（使用负值表示没有限制） 默认 8
        min-idle: 0 # 连接池中的最小空闲连接 默认 0
management:
  endpoint:
    health:
      probes:
        enabled: true
      livenessState:
        enabled: true
      readinessState:
        enabled: true
  endpoints:
    web:
      base-path: /
      path-mapping:
        health: healthz
server:
  port: 8106
oss:
  bucket: xcdemoimg
  endPoint: https://oss-cn-beijing.aliyuncs.com
  accessID: LTAI4FjKaqTSvAoXXgLwDnMe
  accessKeySecret: ******************************
  path: http://xcdemoimg.oss-cn-beijing.aliyuncs.com
  folder: PatrolDataCenter/
knife4j:
  enable: true
  documents:
    -
      group: 2.X版本
      name: 接口签名
      locations: classpath:sign/*
  setting:
    language: zh-CN
    enableSwaggerModels: true
    enableDocumentManage: true
    swaggerModelName: 实体类列表
    enableVersion: false
    enableReloadCacheParameter: false
    enableAfterScript: true
    enableFilterMultipartApiMethodType: POST
    enableFilterMultipartApis: false
    enableRequestCache: true
    enableHost: false
    enableHostText: *************:8005
    enableHomeCustom: true
    homeCustomLocation: classpath:markdown/home.md
    enableSearch: false
    enableFooter: false
    enableFooterCustom: true
    footerCustomContent: 内部使用违规必究
    enableDynamicParameter: false
    enableDebug: true
    enableOpenApi: false
    enableGroup: true
  cors: false
  production: false
  basic:
    enable: true
    username: botSmart.cn
    password: botSmart.cn
customs:
  appKey: 464ff29883e24501ae9a3312df04bf31
  appSecret: 0f6dfce286b24b699f74cf8262771c95

  task:
    rabbitmq-direct: ${customs.task.rabbitmq-direct}
    rabbitmq-queue: ${customs.task.rabbitmq-queue}
    rabbitmq-key: ${customs.task.rabbitmq-key}
#management:
#  endpoints:
#    web:
#      exposure:
#        include: "*"
#  server:
#    port: 10111
#    servlet:
#      context-path: /
#    ssl:
#      enabled: false
#  endpoint:
#    health:
#      show-details: always
