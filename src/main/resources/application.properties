#spring.boot.admin.client.url=http://127.0.0.1:8013
#spring.boot.admin.client.instance.service-base-url=http://localhost:10111
#spring.boot.admin.client.username=SBA_admin
#spring.boot.admin.client.password=SBA_password
#management.endpoints.web.exposure.include=*
spring.quartz.job-store-type=jdbc
spring.quartz.initialize-schema=embedded
spring.quartz.properties.org.quartz.scheduler.instanceName=MyScheduler
spring.quartz.properties.org.quartz.scheduler.instanceId=AUTO
spring.quartz.properties.org.quartz.jobStore.class=org.quartz.impl.jdbcjobstore.JobStoreTX
spring.quartz.properties.org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
spring.quartz.properties.org.quartz.jobStore.tablePrefix=QRTZ_
spring.quartz.properties.org.quartz.jobStore.isClustered=true
spring.quartz.properties.org.quartz.jobStore.misfireThreshold=60000
spring.quartz.properties.org.quartz.jobStore.clusterCheckinInterval=5000
spring.quartz.properties.org.quartz.jobStore.useProperties=false
spring.quartz.properties.org.quartz.jobStore.acquireTriggersWithinLock=true
spring.quartz.properties.org.quartz.threadPool.class=org.quartz.simpl.SimpleThreadPool
spring.quartz.properties.org.quartz.threadPool.threadCount=10
spring.quartz.properties.org.quartz.threadPool.threadPriority=5
spring.quartz.properties.org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread=true
spring.quartz.auto-startup=true
# 初始化后是否自动启动计划程序
spring.quartz.jdbc.comment-prefix=--
# SQL 初始化脚本中单行注释的前缀
spring.quartz.jdbc.initialize-schema=embedded
# 数据库架构初始化模式
spring.quartz.overwrite-existing-jobs=false
# 配置的作业是否应覆盖现有的作业定义
spring.quartz.properties.*=
# 其他石英调度器属性，值是一个 Map
spring.quartz.scheduler-name=quartzScheduler
# 计划程序的名称
spring.quartz.startup-delay=0s
# 初始化完成后启动计划程序的延迟时间
spring.quartz.wait-for-jobs-to-complete-on-shutdown=false
# 关闭时是否等待正在运行的作业完成
