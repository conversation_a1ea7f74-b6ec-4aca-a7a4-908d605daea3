app:
  baseUrl: http://***************:8100/
  datasource:
    host: dev-hw.botsmart.cn:33006
    username: root
    password: Botsmart@2
    name: patrol_info_dispatch
  rabbitmq:
    host: ***************
    port: 5672
    username: saasuser
    password: botsmart.cn
  redis:
    host: ***************
    database: 0
    port: 60379
    password: Botsmart.cn
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

customs:
  patrol-data-center-url: http://***************:8100
  weibo:
    #轻博数据处理
    customer-rabbitmq-queue: queue_weibo_callBackTss
    customer-rabbitmq-direct: direct_weibo_callBackTss
    customer-rabbitmq-key: test_weibo_callBackTs
  props:
    #轻博数据处理
    customer-rabbitmq-queue: queue_test_callBackTss
    customer-rabbitmq-direct: direct_test_callBackTss
    customer-rabbitmq-key: test_callBackTs
    #发送数据异常处理
    customer-queue-fail-callback: queue_fail_callBack
    customer-direct-fail-callback: direct_fail_callBack
    customer-key-fail-callback: data_fail_callBack
  task:
    # 中台任务分发处理
    rabbitmq-task-dispatch-direct: direct_task_dispatch
    rabbitmq-task-dispatch-queue: queue_task_dispatch
    rabbitmq-task-dispatch-key: task_dispatch
    #执行器任务控制 - 运行/停止
    rabbitmq-direct-task-control: direct_task_control
    rabbitmq-key-task-control: task_control
    #执行器任务控制接收后回调状态
    rabbitmq-direct-actuator-callback: direct_actuator_callback
    rabbitmq-queue-actuator-callback: queue_actuator_callback
    rabbitmq-key-actuator-callback: actuator_callback
    #轻博数据处理
    rabbitmq-direct-callback: direct_task_callback
    rabbitmq-queue-callback: queue_task_callback
    rabbitmq-key-callback: key_task_callback
    #回查任务处理-下发
    rabbitmq-direct-lookback: direct_xhw_lookback
    rabbitmq-queue-lookback-webo: queue_xhw_lookback_webo
    rabbitmq-queue-lookback-wechat: queue_xhw_lookback_wechat
    #回查任务处理-回调结果
    rabbitmq-direct-lookback-result: direct_xhw_lookback_result
    rabbitmq-queue-lookback-result-webo: queue_xhw_lookback_result_webo
    rabbitmq-queue-lookback-result-wechat: queue_xhw_lookback_result_wechat
    #回查任务处理-通用key
    rabbitmq-key-lookback-webo: xhw_lookback_webo
    rabbitmq-key-lookback-wechat: xhw_lookback_wechat

internalSystemAddress: http://dev-pgc.botsmart.cn/
patUrl: http://***************:9201/
convertPdfUrl: http://127.0.0.1:9202/Screenshot
thirdPartyPlatformAddress: http://*************:10009/trsdc/
callbackKey: A10A2FFBE5BE4F3C9757E0CF6BABFEE9
qingboUrl: http://projects-databus.gsdata.cn:7777/
qingboProjectId: 10016
qingboSign: 5b8cd6cbe42181fa52f9dc236bf33331
days: -10
proxy:
  targetAddr: http://***************:8100
elasticsearch:
  host: ***************
  port: 9200
  connTimeout: 3000
  socketTimeout: 5000
  connectionRequestTimeout: 500
  #  索引名称
  index-name: call-back-datas
  username: elastic
  password: 123456
  max-conn-total: 20
  max-conn-per-route: 20
weiboAddress: http://127.0.0.1:8888/
runscheduled: 0
lookbackScheduled: 0
weboscheduled: 0
