package com.bot.patrol.info.dispatch.web;

import com.bot.patrol.info.dispatch.infrastructure.annotation.RequiresPermission;
import com.bot.patrol.info.dispatch.infrastructure.constants.CommonResult;
import com.bot.patrol.info.dispatch.service.IQuartzLogService;
import com.bot.patrol.info.dispatch.model.req.*;
import com.bot.patrol.info.dispatch.model.page.QuartzLogPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;
/**
* <AUTHOR>
* @date 2022-03-03
*/
@Api(tags = "任务执行历史")
@RestController
@RequiredArgsConstructor
@RequestMapping("/quartz-log")
public class QuartzLogController {

    private final IQuartzLogService quartzLogService;

    @PostMapping(value = "/add")
    @ApiOperation(value = "新增", notes = "")
    @RequiresPermission("quartz.log.add")
    public CommonResult add(@RequestBody @Valid QuartzLogAddReq request) {
    return quartzLogService.add( request);
    }

    @PostMapping(value = "/update")
    @ApiOperation(value = "修改", notes = "")
    @RequiresPermission("quartz.log.update")
    public CommonResult update(@RequestBody @Valid QuartzLogUpdateReq request) {
    return quartzLogService.update( request);
    }


    @PostMapping(value = "/list")
    @ApiOperation(value = "查询列表不带分页", notes = "")
    @RequiresPermission("quartz.log.list")
    public CommonResult list(@RequestBody @Valid QuartzLogSearchReq request) {
    return quartzLogService.list( request);
    }

    @PostMapping(value = "/page")
    @ApiOperation(value = "查询应用带分页", notes = "")
    @RequiresPermission("quartz.log.page")
    public CommonResult page(@RequestBody @Valid QuartzLogPage request) {
    return quartzLogService.page( request);
    }
}
