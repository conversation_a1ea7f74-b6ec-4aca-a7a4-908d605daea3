package com.bot.patrol.info.dispatch.conver;


import com.bot.patrol.info.dispatch.entity.QuartzJob;
import com.bot.patrol.info.dispatch.model.req.QuartzJobAddReq;
import com.bot.patrol.info.dispatch.model.req.QuartzJobUpdateReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
@Mapper
public interface QuartzJobConvert {
QuartzJobConvert INSTANCE = Mappers.getMapper(QuartzJobConvert.class);

@Mappings({@Mapping(target = "startTime", expression = "java(java.time.LocalDateTime.parse(request.getStartTime(),java.time.format.DateTimeFormatter.ofPattern(\"yyyy-MM-dd HH:mm:ss\")))")})
QuartzJob convert(QuartzJobAddReq request);

@Mappings({})
QuartzJob convert(QuartzJobUpdateReq request);


}