package com.bot.patrol.info.dispatch.service;

import com.bot.patrol.info.dispatch.entity.LookBackUrlList;

import java.util.List;

public interface ILookBackUrlListService {

    LookBackUrlList selectBySid(String itemSid);

    int add(LookBackUrlList lookBackUrlList);
    List<LookBackUrlList> selectByTaskSid(String taskSid);
    boolean getFinishStateByTaskSid(String taskSid);

    int updateEntity(LookBackUrlList lookBackUrlList);

    /**
     * 查询回查任务列表
     * @param taskSid
     * @param url
     * @return
     */
    List<LookBackUrlList> selectByTaskSidAndUrl(String taskSid, String url);
}
