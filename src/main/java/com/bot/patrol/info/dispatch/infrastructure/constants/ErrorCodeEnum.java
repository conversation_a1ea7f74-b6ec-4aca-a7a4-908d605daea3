package com.bot.patrol.info.dispatch.infrastructure.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ErrorCodeEnum {

    SUCCESS(200, "SUCCESS"),

    // 用户信息错误
    USER_NAME_PASS_ERROR(403001, "用户名密码错误"),
    USER_STATE_ERROR(403002, "用户已禁用，请与系统管理员联系"),

    // 系统级错误
    MISSING_PARAM_ERROR(400000, "参数缺失"),
    PARAM_VALUE_ERROR(400001, "参数值不匹配"),
    UN_LOGGED(400100, "未登录"),
    UN_AUTHENTICATED(400200, "无权限"),

    // 上传失败
    UPLOAD_ERROR(500100, "获取OSS签名失败"),
    EXCEL_ERROR(500101, "文件内容为空"),

    // 审核规则错误
    RULES_NAME_REPEAT(400100, "审核规则名称重复"),
    RULES_NOT_EXISTS(400101, "审核规则不存在"),

    // 应用信息错误
    APP_NAME_NULL(400200, "应用名称不能为空"),
    APP_TYPE_NOT_EXISTS(400201, "应用类型不能为空"),
    APP_TYPE_ERROR(400202, "应用类型错误"),
    APP_VERSION_NULL(400203, "应用包名版本不能为空"),
    APP_FILE_NULL(400204, "应用包文件为空"),
    APP_NAME_REPEAT(400205, "应用名称重复"),
    APP_NOT_EXISTS(400206, "应用不存在"),
    APP_UPLOAD_ERROR(400207, "应用上传失败"),
    APP_ANALYSISI_ERROR(400208, "应用解析失败"),
    // 执行器信息错误
    ACTUATOR_API_ERROR(400300, "执行器接口错误"),

    // 脚本信息错误
    SCRIPT_NAME_REPEAT(400400, "脚本名称重复"),
    SCRIPT_NOT_EXISTS(400401, "脚本不存在"),

    // 任务信息错误
    TASK_DESCR_REPEAT(400500, "任务描述重复"),
    TASK_NOT_EXISTS(400501, "任务不存在"),
    TASK_MQ_SEND_ERROR(400502, "任务队列插入失败"),

    // 订单信息错误
    ORDER_SNO_NULL(400600, "订单编号不能为空"),
    ORDER_SNO_FORMAT_ERROR(400601, "订单号格式错误"),
    ORDER_SNO_EXISTSED(400602, "订单编号已存在"),
    ORDER_USER_ID_NULL(400603, "下单用户ID不能为空"),
    ORDER_USER_NAME_NULL(400604, "下单用户名称不能为空"),
    ORDER_USER_PHONE_NULL(400605, "下单用户手机号不能为空"),
    ORDER_USER_PHONE_FORMAT_ERROR(400606, "下单用户手机号格式错误"),
    ORDER_APP_NAME_NULL(400607, "应用名称不能为空"),
    ORDER_APP_UPLOAD_ERROR(400608, "应用上传失败"),
    ORDER_WEB_URL_NULL(400609, "网页类型缺少URL信息"),
    ORDER_TIME_INTERVAL_ERROR(400610, "订单运行时效错误"),
    ORDER_TIME_INTERVAL_FORMAT_ERROR(400611, "订单运行时效格式错误"),
    ORDER_TIME_INTERVAL_LIMITS_ERROR(400612, "订单运行时效区间范围错误"),
    ORDER_NULL(400613, "订单内容为空"),

    // 警告信息
    WARNING_APP_EXISTSED(210600, "订单APP已存在"),

    // 其他
    CONTENT_REPEAT(500200, "内容重复"),
    NO_MORE_CONTENT(500201, "没有更多数据"),
    CONTENT_ERROR(500202, "内容错误"),
    CONTENT_NOT_EXIST(500203, "内容不存在"),
    WORD_REPORT_ERROR(500204, "报告生成失败"),

    //审查
    AUDIT_REPEAT(500300, "审查规则重复"),

    // 登录错误
    CODE_FAILURE(500001, "验证码已过期"),
    CODE_ERROR(500002, "验证码不正确"),
    AUTH_ERROR(500003, "用户名或密码错误"),
    VERIFY_CODE_EXIST_ERROR(500004, "验证码已发送，请勿重复操作"),
    VERIFY_CODE_SEND_ERROR(500005, "验证码发送失败，请验证手机号"),

    // 任务队列错误
    TASK_MQ_CONNECT_ERROR(500401, "中台控制端访问错误"),
    TASK_MQ_INIT_ERROR(500402, "任务初始化失败"),
    //公司错误
    NO_APP_ERROR(600100, "配额不足"),
    NO_COM_ERROR(600101, "所选公司不存在");

    private final int code;
    private final String message;
}