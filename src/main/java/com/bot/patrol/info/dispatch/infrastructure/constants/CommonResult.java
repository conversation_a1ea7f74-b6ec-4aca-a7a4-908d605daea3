package com.bot.patrol.info.dispatch.infrastructure.constants;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommonResult<T> implements Serializable {

    private int code;
    private String msg;
    private T data;

    public static <T> CommonResult<T> success() {
        return CommonResult.<T>builder()
                .code(ErrorCodeEnum.SUCCESS.getCode())
                .msg(ErrorCodeEnum.SUCCESS.getMessage())
                .build();
    }

    public static <T> CommonResult<T> success(T data) {
        return CommonResult.<T>builder()
                .code(ErrorCodeEnum.SUCCESS.getCode())
                .msg(ErrorCodeEnum.SUCCESS.getMessage())
                .data(data)
                .build();
    }

    public static <T> CommonResult<T> error(ErrorCodeEnum errorCodeEnum) {
        return CommonResult.<T>builder()
                .code(errorCodeEnum.getCode())
                .msg(errorCodeEnum.getMessage())
                .build();
    }

    public static <T> CommonResult<T> warning(ErrorCodeEnum errorCodeEnum) {
        return CommonResult.<T>builder()
                .code(errorCodeEnum.getCode())
                .msg(errorCodeEnum.getMessage())
                .build();
    }

    public static <T> CommonResult<T> error(int code, String msg) {
        return CommonResult.<T>builder()
                .code(code)
                .msg(msg)
                .build();
    }
}