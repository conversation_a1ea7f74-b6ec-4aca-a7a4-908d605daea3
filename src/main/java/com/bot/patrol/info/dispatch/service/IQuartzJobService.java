package com.bot.patrol.info.dispatch.service;


import com.bot.patrol.info.dispatch.entity.QuartzJob;
import com.bot.patrol.info.dispatch.infrastructure.constants.CommonResult;
import com.bot.patrol.info.dispatch.model.page.QuartzJobPage;
import com.bot.patrol.info.dispatch.model.req.*;

import java.util.List;

/**
* <AUTHOR>
* @date 2022-03-03
*/

public interface IQuartzJobService {

    CommonResult add(QuartzJobAddReq request);

    int insert(QuartzJob quartzJob);

    boolean add(QuartzJob quartzJob);

    QuartzJob selectByJobName(String taskId);

    QuartzJob selectNoPushedByJobName(String taskSid);

    List<QuartzJob> selectNoPushed();

    CommonResult update(QuartzJobUpdateReq request);

    CommonResult list(QuartzJobSearchReq request);

    CommonResult page(QuartzJobPage request);

    CommonResult updateByCronExpression(QuartzJobUpdateReq request);

    CommonResult pauseAndResume(Long id,Boolean isUsing);
}
