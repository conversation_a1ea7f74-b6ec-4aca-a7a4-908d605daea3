package com.bot.patrol.info.dispatch.model.req;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
* <AUTHOR>
* @date 2022-02-08
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="任务明细",description="任务明细")
public class OrderList implements Serializable {



    // 客服方id
    @ApiModelProperty(value="公司id",name="公司id")
    private String olCpiSid;


    // 应用地址
    @ApiModelProperty(value="任务地址",name="任务地址")
    private String olAiUrl;


    // 备注
    @ApiModelProperty(value="备注（如果是公众号备注是订阅号还是服务号）",name="备注")
    private String olRemark;

    // 应用类型
    @ApiModelProperty(value=" WEB(300300, \"网页\"),\n" +
            "        ANDROID(300301, \"安卓\"),\n" +
            "        IOS(300302, \"IOS\"),\n" +
            "        WCAC(300303, \"公众号\"),\n" +
            "        WCLS(300304,\"微信小程序\"),\n" +
            "        APLS(300305,\"支付宝小程序\"),\n" +
            "        BDLS(300306,\"百度小程序\"),\n" +
            "        API(300307,\"API\"),\n" +
            "        WEIBO(300308,\"微博\");",name="应用类型")
    private Integer olAiSysType;

    // 备注
    @ApiModelProperty(value="应用id",name="应用id")
    private String olAiSid;

    // 名称
    @ApiModelProperty(value="名称",name="名称")
    private String olAiName;




}
