package com.bot.patrol.info.dispatch.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bot.patrol.info.dispatch.conver.QuartzLogConvert;
import com.bot.patrol.info.dispatch.entity.QuartzLog;
import com.bot.patrol.info.dispatch.infrastructure.constants.CommonResult;
import com.bot.patrol.info.dispatch.infrastructure.constants.ErrorCodeEnum;
import com.bot.patrol.info.dispatch.mapper.QuartzLogMapper;
import com.bot.patrol.info.dispatch.model.req.*;
import com.bot.patrol.info.dispatch.model.page.QuartzLogPage;
import com.bot.patrol.info.dispatch.service.IQuartzLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @date 2022-03-03
*/
@Slf4j
@Service
@RequiredArgsConstructor
public class QuartzLogServiceImpl implements IQuartzLogService {

    private final QuartzLogMapper quartzLogMapper;
    @Override
    public CommonResult add(QuartzLogAddReq request) {
        QuartzLog quartzLog = QuartzLogConvert.INSTANCE.convert(request);
        try {
        quartzLogMapper.insert(quartzLog);
        } catch (Exception e) {
        return CommonResult.error(ErrorCodeEnum.CONTENT_REPEAT);
        }
        return CommonResult.success();
    }

    @Override
    public CommonResult update( QuartzLogUpdateReq request) {
        QuartzLog quartzLog = QuartzLogConvert.INSTANCE.convert(request);
        try {
        quartzLogMapper.updateById(quartzLog);
        } catch (Exception e) {
        return CommonResult.error(ErrorCodeEnum.CONTENT_REPEAT);
        }
        return CommonResult.success();
    }
    @Override
    public CommonResult page(QuartzLogPage request) {
    LambdaQueryWrapper<QuartzLog> qw = new LambdaQueryWrapper<QuartzLog>();
            if(ObjectUtils.isNotEmpty(request.getId())){
            qw.eq(QuartzLog::getId, request.getId());
            }
           if(StringUtils.isNotEmpty(request.getBaenName())){
           qw.eq(QuartzLog::getBaenName, request.getBaenName());
           }
           if(StringUtils.isNotEmpty(request.getCronExpression())){
           qw.eq(QuartzLog::getCronExpression, request.getCronExpression());
           }
           if(StringUtils.isNotEmpty(request.getExceptionDetail())){
           qw.eq(QuartzLog::getExceptionDetail, request.getExceptionDetail());
           }
           if(StringUtils.isNotEmpty(request.getJobName())){
           qw.eq(QuartzLog::getJobName, request.getJobName());
           }
           if(StringUtils.isNotEmpty(request.getMethodName())){
           qw.eq(QuartzLog::getMethodName, request.getMethodName());
           }
           if(StringUtils.isNotEmpty(request.getParams())){
           qw.eq(QuartzLog::getParams, request.getParams());
           }
            if(ObjectUtils.isNotEmpty(request.getTime())){
            qw.eq(QuartzLog::getTime, request.getTime());
            }
        Page<QuartzLog> quartzLogPage = quartzLogMapper.selectPage(request, qw);
         return CommonResult.success(quartzLogPage);
    }



    @Override
    public CommonResult list(QuartzLogSearchReq request) {
        List<QuartzLog> quartzLogs=quartzLogMapper.selectList(request);
        return CommonResult.success(quartzLogs);
        }
}