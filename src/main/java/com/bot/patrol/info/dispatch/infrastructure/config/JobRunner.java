package com.bot.patrol.info.dispatch.infrastructure.config;



import lombok.extern.slf4j.Slf4j;
import org.quartz.Scheduler;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> @date 2019-01-07
 */
@Slf4j
@Component
public class JobRunner implements ApplicationRunner {


    @Resource(name = "scheduler")
    private Scheduler scheduler;



    /**
     * 项目启动时重新激活启用的定时任务
     *
     * @param applicationArguments
     * @throws Exception
     */
    @Override
    public void run(ApplicationArguments applicationArguments) {

        System.out.println("--------------------定时任务注入完成---------------------");
    }

}
