package com.bot.patrol.info.dispatch.conver;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bot.patrol.info.dispatch.entity.TaskInfo;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.Map;

@Mapper
public interface TaskInfoConvert {
    TaskInfoConvert INSTANCE = Mappers.getMapper(TaskInfoConvert.class);
    @Mappings({
            @Mapping(target = "sid", expression = "java((String)request.get(\"Sid\"))"),
            @Mapping(target = "tiPiSid", expression = "java(java.util.Objects.isNull(request.get(\"tiPiSid\")) ? null : (String)request.get(\"tiPiSid\"))"),
            @Mapping(target = "tiCpiSid", expression = "java((String)request.get(\"tiCpiSid\"))"),
            @Mapping(target = "tiCpiName", expression = "java((String)request.get(\"tiCpiName\"))"),
            @Mapping(target = "tiType", expression = "java((Integer)request.get(\"tiType\"))"),
            @Mapping(target = "tiSno", expression = "java((String)request.get(\"tiSno\"))"),
            @Mapping(target = "tiDescr", expression = "java((String)request.get(\"tiDescr\"))"),
            @Mapping(target = "tiIndustryType", expression = "java((String)request.get(\"tiIndustryType\"))"),
            @Mapping(target = "tiRunType", expression = "java((Integer)request.get(\"tiRunType\"))"),
            @Mapping(target = "tiRunCycle", expression = "java(java.util.Objects.isNull(request.get(\"tiRunCycle\")) ? null : (Integer)request.get(\"tiRunCycle\"))"),
            @Mapping(target = "tiCycleNum", expression = "java(java.util.Objects.isNull(request.get(\"tiCycleNum\")) ? null : (Integer)request.get(\"tiCycleNum\"))"),
            @Mapping(target = "tiStartTime", expression = "java(com.bot.patrol.info.dispatch.infrastructure.utils.CommUtils.ConvertByMapValue((Map)request.get(\"tiStartTime\")))"),
            @Mapping(target = "tiEndTime", expression = "java(com.bot.patrol.info.dispatch.infrastructure.utils.CommUtils.ConvertByMapValue((Map)request.get(\"tiEndTime\")))"),
            @Mapping(target = "tiMinActuator", expression = "java(java.util.Objects.isNull(request.get(\"tiMinActuator\")) ? null : (Integer)request.get(\"tiMinActuator\"))"),
            @Mapping(target = "tiMaxActuator", expression = "java(java.util.Objects.isNull(request.get(\"tiMaxActuator\")) ? null : (Integer)request.get(\"tiMaxActuator\"))"),
            @Mapping(target = "tiPriority", expression = "java(java.util.Objects.isNull(request.get(\"tiPriority\")) ? 1 : (Integer)request.get(\"tiPriority\"))"),
            @Mapping(target = "tiRunNum", expression = "java(java.util.Objects.isNull((Integer)request.get(\"tiRunNum\")) ? 0 : (Integer)request.get(\"tiRunNum\"))"),
            @Mapping(target = "tiStatus", expression = "java((Integer)request.get(\"tiStatus\"))"),
            @Mapping(target = "tiState", expression = "java((Integer)request.get(\"tiState\"))"),
            @Mapping(target = "tiCreateTime", expression = "java(com.bot.patrol.info.dispatch.infrastructure.utils.CommUtils.ConvertByMapValue((Map)request.get(\"tiCreateTime\")))"),
            @Mapping(target = "tiUpdateTime", expression = "java(com.bot.patrol.info.dispatch.infrastructure.utils.CommUtils.ConvertByMapValue((Map)request.get(\"tiUpdateTime\")))")
    })
    TaskInfo convert(Map<String, Object> request);
}