package com.bot.patrol.info.dispatch.infrastructure.utils;

import com.bot.patrol.info.dispatch.infrastructure.constants.AppConstant;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

public class BCryptUtils {

    private final static Pattern BCRYPT_PATTERN = Pattern.compile("\\A\\$2a?\\$\\d\\d\\$[./0-9A-Za-z]{53}");

    public static String getDefault() {
        return BCryptUtils.sha1Encode(AppConstant.DEFAULT_PASSWORD);
    }

    public static String getPassword(String password) {
        return BCryptUtils.sha1Encode(password);
    }

    public static String sha1Encode(String rawPassword) {
        return BCryptUtils.encode(DigestUtils.sha1Hex(rawPassword));
    }

    private static String encode(String rawPassword) {
        return BCrypt.hashpw(rawPassword, BCrypt.gensalt());
    }

    public static boolean matches(String rawPassword, String encodedPassword) {
        if (StringUtils.isBlank(rawPassword) || StringUtils.isBlank(encodedPassword)) {
            return false;
        }
        if (!BCRYPT_PATTERN.matcher(encodedPassword).matches()) {
            return false;
        }
        return BCrypt.checkpw(getPassword(rawPassword), encodedPassword);
    }
}