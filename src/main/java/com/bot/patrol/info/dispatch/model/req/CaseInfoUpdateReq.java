package com.bot.patrol.info.dispatch.model.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
* <AUTHOR>
* @date 2022-02-16
*/
@ApiModel(value="事例信息对象修改",description="事例信息")
@Data
public class CaseInfoUpdateReq {
        // 任务执行用例sid
        @ApiModelProperty(value="任务执行用例sid",name="任务执行用例sid")
        private String sid;
        // 任务执行用例编号
        @ApiModelProperty(value="任务执行用例编号",name="任务执行用例编号")
        private String ciSno;
        // 执行用例名称
        @ApiModelProperty(value="执行用例名称",name="执行用例名称")
        private String ciCaseName;
        // 归属行业
        @ApiModelProperty(value="归属行业",name="归属行业")
        private String ciIndustryType;
        // 项目id
        @ApiModelProperty(value="项目id",name="项目id")
        private String ciPiSid;
        // 用户id
        @ApiModelProperty(value="用户id",name="用户id")
        private String ciCpiSid;
        // 任务用户实体名称
        @ApiModelProperty(value="任务用户实体名称",name="任务用户实体名称")
        private String ciCpiName;
        // 关联任务sid
        @ApiModelProperty(value="关联任务sid",name="关联任务sid")
        private String ciTiSid;
        // 关联任务编号
        @ApiModelProperty(value="关联任务编号",name="关联任务编号")
        private String ciTiSno;
        // 任务类型
        @ApiModelProperty(value="任务类型",name="任务类型")
        private Integer ciTiType;
        // 关联订单id
        @ApiModelProperty(value="关联订单id",name="关联订单id")
        private String ciOlSid;
        // 关联订单编号
        @ApiModelProperty(value="关联订单编号",name="关联订单编号")
        private String ciOlSno;
        // 关联执行脚本id
        @ApiModelProperty(value="关联执行脚本id",name="关联执行脚本id")
        private String ciSiSid;
        // 执行用例脚本类型
        @ApiModelProperty(value="执行用例脚本类型",name="执行用例脚本类型")
        private Integer ciSiType;
        // 执行用例步骤
        @ApiModelProperty(value="执行用例步骤",name="执行用例步骤")
        private String ciSteps;
        // 执行用例应用包名
        @ApiModelProperty(value="执行用例应用包名",name="执行用例应用包名")
        private String ciAiPackage;
        // 执行用例应用类型
        @ApiModelProperty(value="执行用例应用类型",name="执行用例应用类型")
        private Integer ciAiType;
        // 执行用例应用名称
        @ApiModelProperty(value="执行用例应用名称",name="执行用例应用名称")
        private String ciAiName;
        // 应用地址
        @ApiModelProperty(value="应用地址",name="应用地址")
        private String ciAiUrl;
        // 应用获取来源
        @ApiModelProperty(value="应用获取来源",name="应用获取来源")
        private String ciAiGetOrigin;
        // 任务运行类型
        @ApiModelProperty(value="任务运行类型",name="任务运行类型")
        private Integer ciRunType;
        // 任务周期运行类型
        @ApiModelProperty(value="任务周期运行类型",name="任务周期运行类型")
        private Integer ciRunCycle;
        // 任务周期运行值
        @ApiModelProperty(value="任务周期运行值",name="任务周期运行值")
        private Integer ciCycleNum;
        // 执行用例指定执行器id
        @ApiModelProperty(value="执行用例指定执行器id",name="执行用例指定执行器id")
        private String ciAciSid;
        // 执行用例运行时执行器id
        @ApiModelProperty(value="执行用例运行时执行器id",name="执行用例运行时执行器id")
        private String ciRuntimeAciSid;
        // 名称
        @ApiModelProperty(value="名称",name="名称")
        private String ciAciName;
        // 执行器队列标识
        @ApiModelProperty(value="执行器队列标识",name="执行器队列标识")
        private String ciAciQueueFlag;
        // 执行器访问类型
        @ApiModelProperty(value="执行器访问类型",name="执行器访问类型")
        private Integer ciAciConnType;
        // 执行器设备标识
        @ApiModelProperty(value="执行器设备标识",name="执行器设备标识")
        private String ciAciDevice;
        // 计划开始时间
        @ApiModelProperty(value="计划开始时间",name="计划开始时间")
        private LocalDateTime ciStartTime;
        // 计划结束时间
        @ApiModelProperty(value="计划结束时间",name="计划结束时间")
        private LocalDateTime ciEndTime;
        // 执行器设备网络标识
        @ApiModelProperty(value="执行器设备网络标识",name="执行器设备网络标识")
        private String ciAciDeviceWifi;
        // 操作人
        @ApiModelProperty(value="操作人",name="操作人")
        private String ciOperatorSid;
        // 运行情况 0-停用 1-启用
        @ApiModelProperty(value="运行情况 0-停用 1-启用",name="运行情况 0-停用 1-启用")
        private Integer ciState;
        // 执行用例状态
        @ApiModelProperty(value="执行用例状态",name="执行用例状态")
        private Integer ciStatus;
        // 执行任务优先级
        @ApiModelProperty(value="执行任务优先级",name="执行任务优先级")
        private Integer ciTiPriority;
        // 执行优先级
        @ApiModelProperty(value="执行优先级",name="执行优先级")
        private Integer ciPriority;
        // 最后执行开始时间
        @ApiModelProperty(value="最后执行开始时间",name="最后执行开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime ciLastRunStartTime;
        // 最后执行结束时间
        @ApiModelProperty(value="最后执行结束时间",name="最后执行结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime ciLastRunEndTime;

        @ApiModelProperty(value="执行器服务集群标识",name="执行器服务集群标识")
        private String ciAciServiceFlag;

}
