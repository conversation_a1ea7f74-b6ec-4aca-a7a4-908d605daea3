package com.bot.patrol.info.dispatch.receiver;

import com.alibaba.fastjson.JSONObject;
import com.bot.patrol.info.dispatch.infrastructure.utils.OkHttpUtils;
import com.bot.patrol.info.dispatch.infrastructure.utils.TokenBucketUtils;
import com.bot.patrol.info.dispatch.model.dto.PushDateDTO;
import com.bot.patrol.info.dispatch.service.AsyncTask;
import com.bot.patrol.info.dispatch.service.HttpApiService;
import com.bot.patrol.info.dispatch.service.ILookBackTaskService;
import com.bot.patrol.info.dispatch.service.ILookBackUrlListService;
import com.rabbitmq.client.Channel;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
//@Slf4j
public class CallBackFailConsumerService {
    private static final Logger logger = LoggerFactory.getLogger(CallBackFailConsumerService.class);

    @Autowired
    public HttpApiService httpApiService;
    @Autowired
    private TokenBucketUtils tokenBucketUtils;
    @Autowired
    private ILookBackTaskService lookBackTaskService;
    @Autowired
    private ILookBackUrlListService lookBackUrlListService;
    @Autowired
    private AsyncTask asyncTask;


    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = "${customs.props.customer-queue-fail-callback}", autoDelete = "false"),
                    exchange = @Exchange(value = "${customs.props.customer-direct-fail-callback}", durable = "true", type = ExchangeTypes.DIRECT),
                    key = "${customs.props.customer-key-fail-callback}"
            ), concurrency = "50", ackMode = "MANUAL"
    )
    @RabbitHandler
    public void consumeMessage(Message message, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        String str = new String(message.getBody(), "utf-8");
        //System.out.println("收到消息:" + str);
        try {
            boolean ack = true;
            String datas = null;
            PushDateDTO pushDateDTO = JSONObject.parseObject(str, PushDateDTO.class);
            if (null != pushDateDTO) {
                if (pushDateDTO.getDefaultUrl()) {
                    datas = OkHttpUtils.httpPostJson(pushDateDTO.getUrl(), pushDateDTO.getContent());
                }else {
                    datas = tokenBucketUtils.throttlingCallBack(pushDateDTO.getUrl(), pushDateDTO.getContent());
                }
                if (StringUtils.isEmpty(datas) || (!datas.contains("\"code\":200") && !datas.contains("\"code\":500200") && !datas.contains("\"code\":500203"))) {
                    ack = false;
                    logger.warn("MQ消费，推送数据失败：{}", datas);
                }else {
                    logger.info("MQ消费，推送数据成功：{}" , pushDateDTO.getContent());
                    if (null != pushDateDTO.getLookBackUrlList()){
                        lookBackUrlListService.updateEntity(pushDateDTO.getLookBackUrlList());

                        if(null != pushDateDTO.getLookBackTask()){
                            lookBackTaskService.updateEntity(pushDateDTO.getLookBackTask());
                        }
                    }
                    if (CollectionUtils.isNotEmpty(pushDateDTO.getIdUrlModels())){
                        asyncTask.execTaskA(pushDateDTO.getIdUrlModels());
                    }
                }
            }

            if (ack){
                channel.basicAck(deliveryTag, false);
            }else {
                channel.basicNack(deliveryTag, false, true);
            }
        } catch (Exception e) {
            channel.basicNack(deliveryTag, false, true);
            e.printStackTrace();
        }

    }
}
