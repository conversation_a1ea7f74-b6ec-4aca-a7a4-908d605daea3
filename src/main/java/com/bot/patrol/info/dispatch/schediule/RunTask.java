package com.bot.patrol.info.dispatch.schediule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bot.patrol.info.dispatch.conver.ActuatorInfoConvert;
import com.bot.patrol.info.dispatch.entity.CaseInfo;
import com.bot.patrol.info.dispatch.entity.QuartzJob;
import com.bot.patrol.info.dispatch.entity.ext.ActuatorInfo;
import com.bot.patrol.info.dispatch.infrastructure.constants.CommonResult;
import com.bot.patrol.info.dispatch.infrastructure.constants.EntityTypeEnum;
import com.bot.patrol.info.dispatch.infrastructure.utils.JsonUtils;
import com.bot.patrol.info.dispatch.infrastructure.utils.QyWeChartUtils;
import com.bot.patrol.info.dispatch.infrastructure.utils.RedisUtils;
import com.bot.patrol.info.dispatch.model.req.ActuatorInfoSearchReq;
import com.bot.patrol.info.dispatch.model.req.CaseInfoUpdateReq;
import com.bot.patrol.info.dispatch.model.req.TaskInfoUpdateReq;
import com.bot.patrol.info.dispatch.service.*;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

@Slf4j
@Component
@EnableScheduling
public class RunTask {
    public static RunTask runTask;
    public static List<String> waitingRunList = null;
    public static Boolean isFirstStart = true;
    public static EntityTypeEnum.QuartzPushStateEnum pushState = null;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private IQuartzJobService quartzJobService;

    @Autowired
    private ICaseInfoService iCaseInfoService;

    @Autowired
    private IActuatorService actuatorService;


    @Autowired
    private HttpApiService httpApiService;

    @Autowired
    RabbitTemplate rabbitTemplate;
    @Value("${customs.task.rabbitmq-direct-task-control}")
    private String direct;

    public static void run(String params) {
        log.error(params);
        JSONArray jaRunCases = JSONArray.fromObject(params);
        List<CaseInfo> runCaseList = new ArrayList<>();
        for (int i = 0; i < jaRunCases.size(); i++) {
            runCaseList.add(runTask.iCaseInfoService.selectBySid(jaRunCases.getString(i)));
        }
        Collections.sort(runCaseList, Comparator.comparing(CaseInfo::getCiPriority));
        for (CaseInfo caseInfo : runCaseList) {
            if(caseInfo.getCiStatus().equals(EntityTypeEnum.TaskStatusTypeEnum.Start.getCode())){
                String link=runTask.redisUtils.get("Link-"+caseInfo.getSid());
                if(StringUtils.isEmpty(link)){
                    runTask.redisUtils.set("Link-"+caseInfo.getSid(), "1");
                }
            }else{
                CaseInfoUpdateReq caseInfoUpdateReq=new CaseInfoUpdateReq();
                caseInfoUpdateReq.setSid(caseInfo.getSid());
                caseInfoUpdateReq.setCiStatus(EntityTypeEnum.TaskStatusTypeEnum.Start.getCode());
                CommonResult commonResult1=runTask.httpApiService.caseInfoUpdate(caseInfoUpdateReq);
                if(commonResult1.getCode()==200){
                    String link=runTask.redisUtils.get("Link-"+caseInfo.getSid());
                    if(StringUtils.isEmpty(link)){
                        dealData(caseInfo);
                    }else{
                        runTask.redisUtils.deleteByPrex(link);
                        dealData(caseInfo);
                    }
                }
            }
        }

    }

    public static void dealData(CaseInfo caseInfo){
        JSONObject jsonObject=new JSONObject();
        jsonObject.put("command", "run");
        jsonObject.put("sid", caseInfo.getSid());
        jsonObject.put("siType", caseInfo.getCiSiType());
        jsonObject.put("taskId",caseInfo.getCiTiSid());
        //判断脚本是否绑定执行器
        if(StringUtils.isNotEmpty(caseInfo.getCiAciSid())){
            //直接获取执行器
            ActuatorInfoSearchReq actuatorInfoSearchReq=new ActuatorInfoSearchReq();
            actuatorInfoSearchReq.setSid(caseInfo.getCiAciSid());
            actuatorInfoSearchReq.setAciSoftList(caseInfo.getCiAiPackage());
            getActuator(caseInfo, jsonObject, actuatorInfoSearchReq);
        }else{
            //通过包名获取空闲执行器
            if(StringUtils.isNotEmpty(caseInfo.getCiAiPackage())){
                ActuatorInfoSearchReq actuatorInfoSearchReq=new ActuatorInfoSearchReq();
                actuatorInfoSearchReq.setAciSoftList(caseInfo.getCiAiPackage());
                getActuator(caseInfo, jsonObject, actuatorInfoSearchReq);
            }

        }

    }

    /**
     * 获取执行器
     * @param caseInfo
     * @param jsonObject
     * @param actuatorInfoSearchReq
     */
    public static void getActuator(CaseInfo caseInfo, JSONObject jsonObject, ActuatorInfoSearchReq actuatorInfoSearchReq) {
        if(caseInfo.getCiSiType().equals(1)) {
            actuatorInfoSearchReq.setAciType(EntityTypeEnum.ActuatorTypeEnum.ANDROID.getCode());
        }else {
            actuatorInfoSearchReq.setAciType(EntityTypeEnum.ActuatorTypeEnum.WEB.getCode());
        }
        /**
         * 获取加锁数据
         */
        CommonResult commonResult = runTask.httpApiService.getActuatorInfoByApp(actuatorInfoSearchReq);
        log.error("BBBBBBBBBBBBBBBBBBB"+JSONObject.toJSONString(commonResult));
        if (200 == commonResult.getCode()) {
            if (ObjectUtils.isNotEmpty(commonResult.getData())) {
                ActuatorInfo actuatorInfo = ActuatorInfoConvert.INSTANCE.convert(JsonUtils.jsonToMap(JsonUtils.objectToJson(commonResult.getData())));
                jsonObject.put("name", actuatorInfo.getAciName());
                jsonObject.put("actuatorId", actuatorInfo.getSid());
                CaseInfoUpdateReq caseInfoUpdateReq=new CaseInfoUpdateReq();
                caseInfoUpdateReq.setSid(caseInfo.getSid());
                caseInfoUpdateReq.setCiRuntimeAciSid(actuatorInfo.getSid());
                caseInfoUpdateReq.setCiAciQueueFlag(actuatorInfo.getAciQueueFlag());
                caseInfoUpdateReq.setCiAciServiceFlag(actuatorInfo.getAciServiceFlag());
                caseInfoUpdateReq.setCiAciName(actuatorInfo.getAciName());
                CommonResult commonResult1=runTask.httpApiService.caseInfoUpdate(caseInfoUpdateReq);
                log.error("更新执行器信息"+JSONObject.toJSONString(commonResult1));
                log.error(jsonObject.toString());
                runTask.rabbitTemplate.convertAndSend(runTask.direct, actuatorInfo.getAciQueueFlag(), jsonObject.toString());
                CaseInfo caseInfo1=new CaseInfo();
                BeanUtils.copyProperties(caseInfoUpdateReq, caseInfo1);
                runTask.iCaseInfoService.update(caseInfo1);
            } else {
                dealError(caseInfo);
            }
        } else {
            dealError(caseInfo);
        }
    }

    /**
     * 异常处理
     * @param caseInfo
     */
    public static void dealError(CaseInfo caseInfo){
        /**
         * 设置脚本状态为停止
         */
        CaseInfoUpdateReq caseInfoUpdateReq=new CaseInfoUpdateReq();
        caseInfoUpdateReq.setSid(caseInfo.getSid());
        if(caseInfoUpdateReq.getCiStatus()==null){
            caseInfoUpdateReq.setCiStatus(EntityTypeEnum.TaskStatusTypeEnum.Stop.getCode());
        }
        CommonResult commonResult1=runTask.httpApiService.caseInfoUpdate(caseInfoUpdateReq);
        log.error(JSONObject.toJSONString(commonResult1));
        /**
         * 设置任务为警告
         */
        TaskInfoUpdateReq request=new TaskInfoUpdateReq();
        request.setSid(caseInfo.getCiTiSid());
        request.setTiRunWarning(1);
        CommonResult commonResult=runTask.httpApiService.update(request);
        log.error(JSONObject.toJSONString(commonResult));
        QyWeChartUtils.sendMsgByText("分发通知:脚本运行异常，脚本编号为"+caseInfo.getCiSno());
    }

    @PostConstruct
    public void init() {
        runTask = this;
        if (runTask.isFirstStart) {
            if (Duration.between(LocalDateTime.now(), LocalDateTime.of(LocalDateTime.now().getYear(), LocalDateTime.now().getMonthValue(), LocalDateTime.now().getDayOfMonth(), 23, 0, 0)).toSeconds() > 0) {
                runTask.pushState = EntityTypeEnum.QuartzPushStateEnum.WAIT_PUSH;
            } else {
                int i = 0;
                while (i < 30) {
                    if (runTask.pushState.equals(EntityTypeEnum.QuartzPushStateEnum.WAIT_PUSH)) {
                        try {
                            Thread.sleep(1000);
                            i += 1;
                            if (i == 30) {
                                runTask.pushState = EntityTypeEnum.QuartzPushStateEnum.PUSH_END;
                            }
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    } else {
                        break;
                    }
                }
            }
        }
    }

    @Scheduled(cron = "0 0 0 * * ?")
    //@Scheduled(cron = "0 30 18 ? * WED")
    private static void refreshPushState() {
        runTask.isFirstStart = false;
        runTask.pushState = EntityTypeEnum.QuartzPushStateEnum.WAIT_PUSH;
    }

    @Scheduled(cron = "0 0 23 * * ?")
    private static void jobPush() {
        runTask.pushState = EntityTypeEnum.QuartzPushStateEnum.PUSHING;
        List<QuartzJob> listQuartzJob = runTask.quartzJobService.selectNoPushed();
        for (QuartzJob job : listQuartzJob) {
            if (LocalDateTime.of(LocalDateTime.now().getYear(), LocalDateTime.now().getMonthValue(), LocalDateTime.now().getDayOfMonth(), 0, 0, 0).plusDays(1l).equals(job.getStartTime())) {
                runTask.quartzJobService.add(job);
            }
        }
        runTask.pushState = EntityTypeEnum.QuartzPushStateEnum.PUSH_END;
    }

    public static void intervalJobPush(QuartzJob quartzJob) {
        runTask.quartzJobService.insert(quartzJob);
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                runTask.quartzJobService.add(quartzJob);
            }
        }, Date.from(quartzJob.getStartTime().atZone(ZoneId.systemDefault()).toInstant()));
    }
}
