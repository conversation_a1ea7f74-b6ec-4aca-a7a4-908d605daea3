package com.bot.patrol.info.dispatch.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bot.patrol.info.dispatch.entity.TaskInfo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
public interface TaskInfoMapper extends BaseMapper<TaskInfo> {
    default TaskInfo selectBySid(String sid){
        return selectOne(new LambdaQueryWrapper<TaskInfo>().eq(TaskInfo::getSid, sid));
    }

    default int add(TaskInfo taskInfo){
        return insert(taskInfo);
    }

    default int update(TaskInfo taskInfo){
        return updateById(taskInfo);
    }
}
