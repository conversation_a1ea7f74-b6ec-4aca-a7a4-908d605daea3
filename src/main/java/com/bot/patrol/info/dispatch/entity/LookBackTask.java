package com.bot.patrol.info.dispatch.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("look_back_task")
public class LookBackTask {
    /**
     * sid
     * 回查任务id
     */
    @TableId(value = "sid", type = IdType.ASSIGN_UUID)
    private String sid;
    /**
     * lbt_sno
     * 回查任务编号
     */
    @TableField(value = "lbt_sno")
    private String lbtSno;
    /**
     * lbt_descr
     * 回查任务描述
     */
    @TableField(value = "lbt_descr")
    private String lbtDescr;
    /**
     * lbt_external_id
     * 三方系统id
     */
    @TableField(value = "lbt_external_id")
    private String lbtExternalId;
    /**
     * lbt_origin
     * 任务来源
     */
    @TableField(value = "lbt_origin")
    private Integer lbtOrigin;
    /**
     * lbt_type
     * 回查业务类型
     */
    @TableField(value = "lbt_type")
    private Integer lbtType;
    /**
     * lbt_app_type
     * 回查应用类型
     */
    @TableField(value = "lbt_app_type")
    private Integer lbtAppType;
    /**
     * lbt_status
     * 任务运行状态0-运行1-完成
     */
    @TableField(value = "lbt_state")
    private Integer lbtState;
    /**
     * lbt_callback_url
     * 回调地址
     */
    @TableField(value = "lbt_callback_url")
    private String lbtCallbackUrl;
    /**
     * lbt_create_time
     * 回查任务创建时间
     */
    @TableField(value = "lbt_create_time", insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private String lbtCreateTime;
    /**
     * sid
     * lbt_update_time
     */
    @TableField(value = "lbt_update_time", insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private String lbtUpdateTime;

}
