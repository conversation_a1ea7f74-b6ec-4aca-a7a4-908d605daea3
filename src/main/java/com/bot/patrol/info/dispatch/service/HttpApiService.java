package com.bot.patrol.info.dispatch.service;


import com.bot.patrol.info.dispatch.infrastructure.constants.CommonResult;
import com.bot.patrol.info.dispatch.model.req.*;
import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.Map;

/**
 * 数据中心接口
 */
@Component
@RetrofitClient(baseUrl = "${app.baseUrl}")
public interface HttpApiService {
    @POST("task-info/addByTaskAndOrder")
    CommonResult addByTaskAndOrder(@Body TaskInfoAddReq taskInfoAddReq);

    @POST("task-info/listTaskByOrder")
    CommonResult listTaskByOrder(@Body TaskInfoSearchReq taskInfoSearchReq);

    @POST("task-info/list")
    CommonResult list(@Body TaskInfoSearchReq taskInfoSearchReq);

    @POST("task-info/listTaskByOrderUrl")
    CommonResult listTaskByOrderUrl(@Body TaskInfoSearchReq taskInfoSearchReq);

    @POST("case-info/update")
    CommonResult caseInfoUpdate(@Body CaseInfoUpdateReq request );

    @POST("task-info/updateByExternalId")
    CommonResult updateByExternalId(@Body TaskInfoUpdateReq request );

    @POST("actuator-info/getActuatorInfoByApp")
    CommonResult getActuatorInfoByApp(@Body ActuatorInfoSearchReq request );

    @POST("task-info/update")
    CommonResult update(@Body TaskInfoUpdateReq request );

    @POST("actuator-info/update")
    CommonResult actuatorUpdate(@Body ActuatorInfoUpdateReq request );


    @POST("order-list/getOneByList")
    CommonResult getOneByList(@Body OrderListSearchReq request );

    @POST("order-list/listByWeiBo")
    CommonResult listByWeiBo(@Body OrderListSearchReq request );

    @POST("task-info/getRunTaskCountByOlAiUrl")
    CommonResult getRunTaskCountByOlAiUrl(@Body RunTaskCountByOlAiUrlReq request );

    @POST("task-info/getSnoByUrl")
    CommonResult getSnoByUrl(@Body RunTaskCountByOlAiUrlReq request );
}
