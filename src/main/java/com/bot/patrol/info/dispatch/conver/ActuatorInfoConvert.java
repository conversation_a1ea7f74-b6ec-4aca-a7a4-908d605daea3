package com.bot.patrol.info.dispatch.conver;

import com.bot.patrol.info.dispatch.entity.ext.ActuatorInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;
import java.util.Map;

@Mapper
public interface ActuatorInfoConvert {

    ActuatorInfoConvert INSTANCE = Mappers.getMapper(ActuatorInfoConvert.class);

    @Mappings({
            @Mapping(target = "sid", expression = "java((String)request.get(\"sid\"))"),
            @Mapping(target = "aciCpiSid", expression = "java(java.util.Objects.isNull(request.get(\"aciCpiSid\")) ? null : (String)request.get(\"aciCpiSid\"))"),
            @Mapping(target = "aciQueueFlag", expression = "java((String)request.get(\"aciQueueFlag\"))"),
            @Mapping(target = "aciType", expression = "java((Integer)request.get(\"aciType\"))"),
            @Mapping(target = "aciConnType", expression = "java(java.util.Objects.isNull(request.get(\"aciConnType\")) ? null : (Integer)request.get(\"aciConnType\"))"),
            @Mapping(target = "aciVersion", expression = "java((String)request.get(\"aciVersion\"))"),
            @Mapping(target = "aciName", expression = "java((String)request.get(\"aciName\"))"),
            @Mapping(target = "aciIp", expression = "java((String)request.get(\"aciIp\"))"),
            @Mapping(target = "aciPort", expression = "java((Integer)request.get(\"aciPort\"))"),
            @Mapping(target = "aciDevice", expression = "java((String)request.get(\"aciDevice\"))"),
            @Mapping(target = "aciDeviceWifi", expression = "java(java.util.Objects.isNull(request.get(\"aciDeviceWifi\")) ? null : (String)request.get(\"aciDeviceWifi\"))"),
            @Mapping(target = "aciSystem", expression = "java((String)request.get(\"aciSystem\"))"),
            @Mapping(target = "aciArch", expression = "java((String)request.get(\"aciArch\"))"),
            @Mapping(target = "aciSystemVersion", expression = "java((String)request.get(\"aciSystemVersion\"))"),
            @Mapping(target = "aciCpu", expression = "java((String)request.get(\"aciCpu\"))"),
            @Mapping(target = "aciRam", expression = "java((String)request.get(\"aciRam\"))"),
            @Mapping(target = "aciStatus", expression = "java((Integer)request.get(\"aciStatus\"))"),
            @Mapping(target = "aciSoftList", expression = "java(java.util.Objects.isNull(request.get(\"aciSoftList\")) ? null : (String)request.get(\"aciSoftList\"))"),
            @Mapping(target = "aciIsTest", expression = "java((Integer)request.get(\"aciIsTest\"))"),
            @Mapping(target = "aciOperator", expression = "java((String)request.get(\"aciOperator\"))"),
            @Mapping(target = "aciCreateTime", expression = "java(java.util.Objects.isNull(request.get(\"ciLastRunStartTime\")) ? null : java.time.LocalDateTime.parse((String)request.get(\"aciCreateTime\"), java.time.format.DateTimeFormatter.ofPattern(\"yyyy-MM-dd HH:mm:ss\")))"),
            @Mapping(target = "aciUpdateTime", expression = "java(java.util.Objects.isNull(request.get(\"ciLastRunStartTime\")) ? null : java.time.LocalDateTime.parse((String)request.get(\"aciUpdateTime\"), java.time.format.DateTimeFormatter.ofPattern(\"yyyy-MM-dd HH:mm:ss\")))"),
            @Mapping(target = "aciServiceFlag", expression = "java((String)request.get(\"aciServiceFlag\"))"),
            @Mapping(target = "aciServiceName", expression = "java(java.util.Objects.isNull(request.get(\"aciServiceName\")) ? null : (String)request.get(\"aciServiceName\"))"),
            @Mapping(target = "aciIsStop", expression = "java((Integer)request.get(\"aciIsStop\"))"),
            @Mapping(target = "ext", expression = "java(java.util.Objects.isNull(request.get(\"ext\")) ? null : (String)request.get(\"ext\"))")

    })
    ActuatorInfo convert(Map<String, Object> request);
}