package com.bot.patrol.info.dispatch.service;

import com.bot.patrol.info.dispatch.entity.LookBackTask;

import java.util.List;

public interface ILookBackTaskService {

    LookBackTask selectBySid(String taskSid);

    LookBackTask selectByTaskNo(String taskNo);
    int add(LookBackTask lookBackTask);
    List<LookBackTask> selectByState(int state);

    int updateEntity(LookBackTask lookBackTask);
}
