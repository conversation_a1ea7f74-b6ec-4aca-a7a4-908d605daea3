package com.bot.patrol.info.dispatch.schediule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bot.patrol.info.dispatch.entity.LookBackTask;
import com.bot.patrol.info.dispatch.entity.LookBackUrlList;
import com.bot.patrol.info.dispatch.entity.TaskInfo;
import com.bot.patrol.info.dispatch.infrastructure.constants.CommonResult;
import com.bot.patrol.info.dispatch.infrastructure.constants.EntityTypeEnum;
import com.bot.patrol.info.dispatch.infrastructure.pool.FixedThreadPool;
import com.bot.patrol.info.dispatch.infrastructure.utils.*;
import com.bot.patrol.info.dispatch.model.ESModel;
import com.bot.patrol.info.dispatch.model.ESModelDetail;
import com.bot.patrol.info.dispatch.model.IdUrlModel;
import com.bot.patrol.info.dispatch.model.Position;
import com.bot.patrol.info.dispatch.model.dto.PushDateDTO;
import com.bot.patrol.info.dispatch.model.req.TaskInfoSearchReq;
import com.bot.patrol.info.dispatch.model.req.TaskInfoUpdateReq;
import com.bot.patrol.info.dispatch.service.HttpApiService;
import com.bot.patrol.info.dispatch.service.ILookBackTaskService;
import com.bot.patrol.info.dispatch.service.ILookBackUrlListService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.awt.*;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 定时任务
 */
@Slf4j
@Component
public class ScheduledService {

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private TokenBucketUtils tokenBucketUtils;

    @Autowired
    public HttpApiService httpApiService;

    @Autowired
    private ElasticsearchTemplate elasticsearchTemplate;

    @Autowired
    private ILookBackTaskService lookBackTaskService;

    @Autowired
    private ILookBackUrlListService lookBackUrlListService;

    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Value("${customs.task.rabbitmq-task-dispatch-direct}")
    private String direct;
    @Value("${customs.task.rabbitmq-task-dispatch-key}")
    private String key;

    @Value("${convertPdfUrl}")
    private String convertPdfUrl;


    private List<String> listGroup=new ArrayList<String>(){
        {
            this.add("董测试");
        }
    };

    private List<String> lookBackGroup=new ArrayList<String>(){
        {
            this.add("回查任务");
        }
    };

    private List<String> listImg=new ArrayList<String>(){
        {
            this.add(".gif");
            this.add(".jpeg");
            this.add(".png");
            this.add(".jpg");
            this.add(".bmp");
            this.add(".GIF");
            this.add(".JPEG");
            this.add(".PNG");
            this.add(".JPG");
            this.add(".BMP");
        }
    };



    private String url="https://advancedapi.bazhuayu.com/";
    /**
     * 内部系统地址
     */
    @Value("${internalSystemAddress}")
    private String internalSystemAddress;

//    /**
//     * 正式
//     */
//    private String internalSystemAddress="https://pgc.botsmart.cn/";
    /**
     * 三方平台地址
     */
    @Value("${thirdPartyPlatformAddress}")
    private String thirdPartyPlatformAddress;

    @Value("${runscheduled}")
    private String runscheduled;

    @Value("${lookbackScheduled}")
    private String lookbackScheduled;

    @Value("${customs.props.customer-direct-fail-callback}")
    private String directFcb;
    @Value("${customs.props.customer-key-fail-callback}")
    private String keyFcb;

    //@Scheduled(cron = "0 */1 * * * ?")
    /*public void test() throws IOException, InterruptedException, AWTException {
        GetBZYToken getBZYToken=new GetBZYToken(redisUtils);
        String taskGroupData=OkHttpUtils.httpGet(url+"api/TaskGroup", getBZYToken.getToken());
        JSONArray jsonArray=getBZYData(taskGroupData);
        for (int i = 0; i < jsonArray.size(); i++) {
            if(!"重庆采集测试".equals(jsonArray.getJSONObject(i).getString("taskGroupName"))){
                continue;
            }
            String taskData=OkHttpUtils.httpGet(url+"api/Task?taskGroupId="+jsonArray.getJSONObject(i).getInteger("taskGroupId"), getBZYToken.getToken());
            JSONArray taskDataJsonArray=getBZYData(taskData);
            for (int j = 0; j <taskDataJsonArray.size() ; j++) {
                if(!"XH-168776942323748-3_复制".equals(taskDataJsonArray.getJSONObject(j).getString("taskName"))){
                    continue;
                }else{
                    System.out.println("打印");
                    TaskInfoSearchReq taskInfoSearchReq=new TaskInfoSearchReq();
                    List<Integer> typeList=new ArrayList<>();
                    *//*typeList.add(302105);
                    typeList.add(302106);*//*
                    taskInfoSearchReq.setTypeList(typeList);
                    CommonResult commonResult=httpApiService.list(taskInfoSearchReq);
                    List<TaskInfo> list=new ArrayList<>();
                    if(200==commonResult.getCode()){
                        if(ObjectUtils.isNotEmpty(commonResult.getData())){
                            list= (List<TaskInfo>) commonResult.getData();
                        }
                    }
                    for (int k1 = 0; k1 < list.size(); k1++) {
                        TaskInfo taskInfo = JSONObject.parseObject(JSONObject.toJSONString(list.get(k1)), TaskInfo.class);
                        if ("XH-165813004103976".indexOf(taskInfo.getTiSno()) >= 0) {
                        //if (taskDataJsonArray.getJSONObject(j).getString("taskName").indexOf(taskInfo.getTiSno()) >= 0) {
                        //if (true) {
                            String modelName = taskDataJsonArray.getJSONObject(j).getString("taskName").replaceAll(taskInfo.getTiSno() + "-", "");
                            taskInfo.setTiDescr(taskDataJsonArray.getJSONObject(j).getString("taskName"));
                            //String taskId = taskDataJsonArray.getJSONObject(j).getString("taskId");
                            String s1=OkHttpUtils.httpGet(url+"api/notexportdata/gettop?taskId="+ taskDataJsonArray.getJSONObject(j).getString("taskId")+"&size=1000", getBZYToken.getToken());
                            JSONObject jsonObject1 =JSON.parseObject(s1);
                            if("success".equals(jsonObject1.getString("error"))) {
                                JSONObject data = jsonObject1.getJSONObject("data");
                                JSONArray jsonArray1 = data.getJSONArray("dataList");
                                if (ObjectUtils.isNotEmpty(jsonArray1)) {
                                    TaskInfoUpdateReq request = new TaskInfoUpdateReq();
                                    request.setSid(taskInfo.getSid());
                                    request.setTiRunNum(taskInfo.getTiRunNum() + 1);
                                    CommonResult commonResult1 = httpApiService.update(request);
                                    log.info(JSONObject.toJSONString(commonResult1));
                                    for (int k = 0; k < jsonArray1.size(); k++) {
                                        JSONObject jsonObject2 = jsonArray1.getJSONObject(k);
                                        log.info("从八爪鱼获取到的数据" + jsonObject2);
                                        String title = jsonObject2.getString("标题");
                                        String titleUrl = jsonObject2.getString("标题链接");
                                        String time = jsonObject2.getString("时间");
                                        String html = jsonObject2.containsKey("html") ? jsonObject2.getString("html") : null;
                                        jsonObject2.remove("标题");
                                        jsonObject2.remove("标题链接");
                                        jsonObject2.remove("时间");
                                        if (Objects.nonNull(html)) {
                                            jsonObject2.remove("html");
                                        }
                                        Map<String, String> jsonMap = JSONObject.toJavaObject(jsonObject2, Map.class);
                                        ESModel jsonObject = new ESModel();
//                        jsonObject.setCompanyId(taskInfo.getTiCpiSid());
                                        jsonObject.setId(taskInfo.getSid());
                                        jsonObject.setProjectId(null);
                                        jsonObject.setDataId(UUID.randomUUID().toString().trim().replaceAll("-", ""));
                                        jsonObject.setTitle(title);
                                        jsonObject.setAppId(null);
                                        jsonObject.setTaskId(taskInfo.getTiExternalId());
                                        jsonObject.setCaseId(null);
                                        jsonObject.setHitUrl(titleUrl);
                                        if (StringUtils.isNotEmpty(time)) {
                                            jsonObject.setScreenTime(ifTime(time));
                                        } else {
                                            DateTimeFormatter dfg = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                                            String string = new SimpleDateFormat("yyyy-MM-dd").format(new Date()).toString();
                                            String time1 = new SimpleDateFormat("HH:mm:ss").format(new Date()).toString();
                                            jsonObject.setScreenTime(LocalDateTime.parse(string + " " + time1, dfg));
                                        }
                                        jsonObject.setEventId(null);
                                        jsonObject.setSearchWordId(null);
                                        jsonObject.setModuleName(modelName);
                                        jsonObject.setTypeCode(taskInfo.getTiType());
                                        jsonObject.setReadTheNumber("0");
                                        jsonObject.setThumbUpFor("0");
                                        jsonObject.setLookingAtTheNumber("0");
                                        jsonObject.setForwardingNumber("0");
                                        jsonObject.setComments("0");
                                        List<ESModelDetail> esModelDetails = new ArrayList<>();
                                        List<String> im = new ArrayList<>();
                                        StringBuffer stringBuffer = new StringBuffer();
                                        for (String key : jsonMap.keySet()) {
                                            String value = jsonMap.get(key);
                                            if (isHttpUrl(value)) {
                                                for (int l = 0; l < listImg.size(); l++) {
                                                    if (value.indexOf(listImg.get(l)) >= 0) {
                                                        im.add(value);
                                                    }
                                                }
                                            } else {
                                                ESModelDetail esModelDetail = new ESModelDetail();
                                                esModelDetail.setWord(value);
                                                Position position = new Position();
                                                esModelDetail.setPosition(position);
                                                esModelDetails.add(esModelDetail);
                                                if (StringUtils.isNotEmpty(value)) {
                                                    value = replaceBlank(value);
                                                }
                                                stringBuffer.append(value);
                                            }
                                        }
                                        jsonObject.setText(stringBuffer.toString());
                                        jsonObject.setEsModelDetailList(esModelDetails);
                                        jsonObject.setImgList(im);
                                        jsonObject.setCallBackUrl(null);
                                        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                                        LocalDateTime ldt = LocalDateTime.parse(jsonObject.getScreenTime().format(df), df);
                                        jsonObject.setScreenTime(ldt);
                                        System.out.println(jsonObject.getId() + taskInfo.getTiDescr());
                                        //log.info(JSONObject.toJSONString(jsonObject));
                                        List<IdUrlModel> idUrlModels = new ArrayList<>();
                                        IdUrlModel idUrlModel = new IdUrlModel();
                                        idUrlModel.setId(jsonObject.getDataId());
                                        idUrlModel.setUrl(jsonObject.getHitUrl());
                                        idUrlModels.add(idUrlModel);
                                        *//*String postJson = OkHttpUtils.httpPostJson(convertPdfUrl, JSONObject.toJSONString(idUrlModels));
                                        System.out.println("请求html转换返回数据:" + postJson);*//*

                                        boolean defaultUrl = false;
                                        String url = taskInfo.getTiExternalCallBackUrl();
                                        String content = JSONObject.toJSONString(jsonObject);
                                        String datas = null;
                                        if (StringUtils.isNotEmpty(url)) {
                                            //调用回调pushData：定时任务
                                            //String datas=OkHttpUtils.httpPostJson(taskInfo.getTiExternalCallBackUrl(), JSONObject.toJSONString(jsonObject));
                                            datas = tokenBucketUtils.throttlingCallBack(url, content);
                                        } else {
                                            defaultUrl = true;
                                            url = internalSystemAddress + "api/third/patrolAudit";
                                            datas = OkHttpUtils.httpPostJson(url, content);
                                        }
                                        if (StringUtils.isEmpty(datas) || (!datas.contains("\"code\":200") && !datas.contains("\"code\":500200"))) {
                                            //发送至mq
                                            PushDateDTO pushDateDTO = new PushDateDTO();
                                            pushDateDTO.setUrl(url);
                                            pushDateDTO.setContent(content);
                                            pushDateDTO.setDefaultUrl(defaultUrl);
                                            rabbitTemplate.convertAndSend(directFcb, keyFcb, JSONObject.toJSONString(pushDateDTO));
                                            log.info("推送数据失败：{}" , datas);
                                        } else {
                                            log.info("推送数据成功：{}" , content);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        log.info("=====>>>>>使用cron  {}",System.currentTimeMillis());
    }*/


//    @Scheduled(cron = "0 */1 * * * ?")
//    public void test() throws IOException, InterruptedException, AWTException {
//        GetBZYToken getBZYToken=new GetBZYToken(redisUtils);
//        String taskGroupData=OkHttpUtils.httpGet(url+"api/TaskGroup", getBZYToken.getToken());
//        JSONArray jsonArray=getBZYData(taskGroupData);
//        for (int i = 0; i < jsonArray.size(); i++) {
//            if(!"重庆采集测试".equals(jsonArray.getJSONObject(i).getString("taskGroupName"))){
//                continue;
//            }
//            String taskData=OkHttpUtils.httpGet(url+"api/Task?taskGroupId="+jsonArray.getJSONObject(i).getInteger("taskGroupId"), getBZYToken.getToken());
//            JSONArray taskDataJsonArray=getBZYData(taskData);
//            for (int j = 0; j <taskDataJsonArray.size() ; j++) {
//                if(!"XH-168776942323748-3_复制".equals(taskDataJsonArray.getJSONObject(j).getString("taskName"))){
//                    continue;
//                }else{
//                    String s1=OkHttpUtils.httpGet(url+"api/notexportdata/gettop?taskId="+ taskDataJsonArray.getJSONObject(j).getString("taskId")+"&size=1000", getBZYToken.getToken());
//                    JSONObject jsonObject1 =JSON.parseObject(s1);
//                    if("success".equals(jsonObject1.getString("error"))) {
//                        JSONObject data = jsonObject1.getJSONObject("data");
//                        JSONArray jsonArray1 = data.getJSONArray("dataList");
//                        if (ObjectUtils.isNotEmpty(jsonArray1)) {
//                            for (int k = 0; k < jsonArray1.size(); k++) {
//                                Document document = Jsoup.parse(jsonArray1.getJSONObject(k).getString("html"));
//                                List<String> list = document.select("abb").eachAttr("src");
//                                int a =0;
//                                for (int k1 = 0; k1 < list.size(); k1++) {
//
//                                }
//                            }
//                        }
//                    }
//                }
////                for (int k1 = 0; k1 < list.size(); k1++) {
////                    TaskInfo taskInfo=JSONObject.parseObject(JSONObject.toJSONString(list.get(k1)), TaskInfo.class);
////                    if(taskDataJsonArray.getJSONObject(j).getString("taskName").indexOf(taskInfo.getTiSno())>=0){
////                        String modelName=taskDataJsonArray.getJSONObject(j).getString("taskName").replaceAll(taskInfo.getTiSno()+"-","");
////                        taskInfo.setTiDescr(taskDataJsonArray.getJSONObject(j).getString("taskName"));
////                        String taskId=taskDataJsonArray.getJSONObject(j).getString("taskId");
////                        if(ObjectUtils.isEmpty(redisUtils.get(taskId))){
////                            log.info("添加到队列"+taskId);
////                            Thread.sleep(100);
////                            FixedThreadPool.submit(taskId, () -> {
////                                pushDate( getBZYToken,  taskInfo, taskId,modelName);
////                            });
////                        }
////                    }
////
////                }
//            }
//
//        }
//        log.info("=====>>>>>使用cron  {}",System.currentTimeMillis());
//    }
    /**
     *获取任务数据
     */
    @Scheduled(cron = "0 */15 * * * ?")
    public void scheduledByGetData() throws IOException, InterruptedException, AWTException {
        if("1".equals(runscheduled)){
            TaskInfoSearchReq taskInfoSearchReq=new TaskInfoSearchReq();
            List<Integer> typeList=new ArrayList<>();
            typeList.add(302105);
            typeList.add(302106);
            taskInfoSearchReq.setTypeList(typeList);
            CommonResult commonResult=httpApiService.list(taskInfoSearchReq);
            List<TaskInfo> list=new ArrayList<>();
            if(200==commonResult.getCode()){
                if(ObjectUtils.isNotEmpty(commonResult.getData())){
                    list= (List<TaskInfo>) commonResult.getData();
                }
            }
            GetBZYToken getBZYToken=new GetBZYToken(redisUtils);
            //查询八爪鱼任务分组
            String taskGroupData=OkHttpUtils.httpGet(url+"api/TaskGroup", getBZYToken.getToken());
            JSONArray jsonArray=getBZYData(taskGroupData);
            for (int i = 0; i < jsonArray.size(); i++) {
                System.out.println(listGroup.contains(jsonArray.getJSONObject(i).getString("taskGroupName")));
                if(!listGroup.contains(jsonArray.getJSONObject(i).getString("taskGroupName"))){
                    continue;
                }
                //查询八爪鱼任务数据
                String taskData=OkHttpUtils.httpGet(url+"api/Task?taskGroupId="+jsonArray.getJSONObject(i).getInteger("taskGroupId"), getBZYToken.getToken());
                JSONArray taskDataJsonArray=getBZYData(taskData);
                for (int j = 0; j <taskDataJsonArray.size() ; j++) {
                    for (int k1 = 0; k1 < list.size(); k1++) {
                        TaskInfo taskInfo=JSONObject.parseObject(JSONObject.toJSONString(list.get(k1)), TaskInfo.class);
                        if(taskDataJsonArray.getJSONObject(j).getString("taskName").indexOf(taskInfo.getTiSno())>=0){
                            String modelName=taskDataJsonArray.getJSONObject(j).getString("taskName").replaceAll(taskInfo.getTiSno()+"-","");
                            taskInfo.setTiDescr(taskDataJsonArray.getJSONObject(j).getString("taskName"));
                            String taskId=taskDataJsonArray.getJSONObject(j).getString("taskId");
                            if(ObjectUtils.isEmpty(redisUtils.get(taskId))){
                                log.info("回查推送添加至队列"+taskId);
                                Thread.sleep(100);
                                FixedThreadPool.submit(taskId, () -> {
                                    pushDate( getBZYToken,  taskInfo, taskId,modelName);
                                });
                            }
                        }

                    }
                }

            }
            log.info("=====>>>>>使用cron  {}",System.currentTimeMillis());
        }
    }

    @Scheduled(cron = "0 */5 * * * ?")
    public void scheduledByLookBackGetData() throws IOException, InterruptedException, AWTException {
        if("1".equals(lookbackScheduled)){
            List<LookBackTask> list= lookBackTaskService.selectByState(0);
            if(list.size() == 0){
                return;
            }
            GetBZYToken getBZYToken=new GetBZYToken(redisUtils);
            String taskGroupData=OkHttpUtils.httpGet(url+"api/TaskGroup", getBZYToken.getToken());
            JSONArray jsonArray=getBZYData(taskGroupData);
            for (int i = 0; i < jsonArray.size(); i++) {
                System.out.println(lookBackGroup.contains(jsonArray.getJSONObject(i).getString("taskGroupName")));
                if(!lookBackGroup.contains(jsonArray.getJSONObject(i).getString("taskGroupName"))){
                    continue;
                }
                String taskData=OkHttpUtils.httpGet(url+"api/Task?taskGroupId="+jsonArray.getJSONObject(i).getInteger("taskGroupId"), getBZYToken.getToken());
                JSONArray taskDataJsonArray=getBZYData(taskData);
                for (int j = 0; j <taskDataJsonArray.size() ; j++) {
                    for (int k1 = 0; k1 < list.size(); k1++) {
                        LookBackTask lookBackTask=JSONObject.parseObject(JSONObject.toJSONString(list.get(k1)), LookBackTask.class);
                        if(taskDataJsonArray.getJSONObject(j).getString("taskName").indexOf(lookBackTask.getLbtSno())>=0){
                            String modelName=taskDataJsonArray.getJSONObject(j).getString("taskName").replaceAll(lookBackTask.getLbtSno()+"-","");
                            lookBackTask.setLbtDescr(taskDataJsonArray.getJSONObject(j).getString("taskName"));
                            String taskId=taskDataJsonArray.getJSONObject(j).getString("taskId");
                            if(ObjectUtils.isEmpty(redisUtils.get(taskId))) {
                                log.info("添加到队列"+taskId);
                                Thread.sleep(1000);
                                FixedThreadPool.submit(taskId, () -> {
                                    pushDate(getBZYToken, lookBackTask, taskId, modelName);
                                });
                            }
                        }

                    }
                }

            }
            log.info("=====>>>>>使用cron  {}",System.currentTimeMillis());
        }
    }

    /**
     * 八爪鱼数据推送：
     * @param getBZYToken
     * @param lookBackTask
     * @param taskId
     * @param modelName
     */
    private void pushDate(GetBZYToken getBZYToken, LookBackTask lookBackTask,String taskId ,String modelName) {
        redisUtils.set(taskId, lookBackTask.getSid());
        try{
            String s1=OkHttpUtils.httpGet(url+"api/notexportdata/gettop?taskId="+ taskId+"&size=1000", getBZYToken.getToken());
            JSONObject jsonObject1 =JSON.parseObject(s1);
            if("success".equals(jsonObject1.getString("error"))){
                JSONObject data=jsonObject1.getJSONObject("data");
                JSONArray jsonArray1 =data.getJSONArray("dataList");
                if(ObjectUtils.isNotEmpty(jsonArray1)){
                    for (int k = 0; k < jsonArray1.size(); k++) {
                        JSONObject jsonObject2=jsonArray1.getJSONObject(k);
                        log.info("从八爪鱼获取到的数据"+jsonObject2);
                        String title=jsonObject2.getString("标题");
                        String titleUrl=jsonObject2.getString("标题链接");
                        String time=jsonObject2.getString("时间");
                        String html=jsonObject2.containsKey("html") ? jsonObject2.getString("html") : null;
                        jsonObject2.remove("标题");
                        jsonObject2.remove("标题链接");
                        jsonObject2.remove("时间");
                        if(Objects.nonNull(html)){
                            jsonObject2.remove("html");
                        }
                        Map<String, String> jsonMap = JSONObject.toJavaObject(jsonObject2, Map.class);
                        ESModel jsonObject=new ESModel();
                        jsonObject.setId(lookBackTask.getSid());
                        jsonObject.setProjectId(null);
                        List<LookBackUrlList> lookBackUrlList = lookBackUrlListService.selectByTaskSidAndUrl(lookBackTask.getSid(), titleUrl);
                        if (Objects.isNull(lookBackUrlList) || lookBackUrlList.isEmpty()){
                            continue;
                        }

                        jsonObject.setTitle(title);
                        jsonObject.setAppId(null);
                        jsonObject.setTaskId(lookBackTask.getLbtExternalId());
                        jsonObject.setCaseId(null);
                        jsonObject.setHitUrl(titleUrl);
                        if(StringUtils.isNotEmpty(time)){
                            jsonObject.setScreenTime(ifTime(time));
                        }else {
                            DateTimeFormatter dfg = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            String string = new SimpleDateFormat("yyyy-MM-dd").format(new Date()).toString();
                            String time1 = new SimpleDateFormat("HH:mm:ss").format(new Date()).toString();
                            jsonObject.setScreenTime(LocalDateTime.parse(string+" "+time1,dfg));
                        }
                        jsonObject.setEventId(null);
                        jsonObject.setSearchWordId(null);
                        jsonObject.setModuleName(modelName);
                        jsonObject.setTypeCode(lookBackTask.getLbtType());
                        jsonObject.setReadTheNumber("0");
                        jsonObject.setThumbUpFor("0");
                        jsonObject.setLookingAtTheNumber("0");
                        jsonObject.setForwardingNumber("0");
                        jsonObject.setComments("0");
                        List<ESModelDetail> esModelDetails=new ArrayList<>();
                        List<String> im=new ArrayList<>();
                        StringBuffer stringBuffer = new StringBuffer();
                        for (String key : jsonMap.keySet()) {
                            String value = jsonMap.get(key);
                            if(isHttpUrl(value)){
                                for (int l = 0; l < listImg.size(); l++) {
                                    if(value.indexOf(listImg.get(l))>=0){
                                        im.add(value);
                                    }
//                                    else{
//                                        jsonObject.setHitUrl(value);
//                                    }
                                }

                            }else{
                                ESModelDetail esModelDetail=new ESModelDetail();
                                esModelDetail.setWord(value);
                                Position position=new Position();
                                esModelDetail.setPosition(position);
                                esModelDetails.add(esModelDetail);
                                if(StringUtils.isNotEmpty(value)){
                                    value=replaceBlank(value);
                                }
                                stringBuffer.append(value);
                            }
                        }
                        jsonObject.setText(stringBuffer.toString());
                        jsonObject.setEsModelDetailList(esModelDetails);
//                        List<String> attachmentList=new ArrayList<>();
//                        List<String> veidoList=new ArrayList<>();
//                        if (html != null) {
//                            Document document = Jsoup.parse(html);
//                            List<String> imgs = document.select("img").eachAttr("src");
//                            if(!imgs.isEmpty()){
//                                for(String imgUrl: imgs) {
//                                    im.add(CommUtils.parseSrc(imgUrl, titleUrl));
//                                }
//                            }
//                            List<String> attachments = document.select("a").eachAttr("href");
//                            if (!attachments.isEmpty()) {
//                                for(String attachmentUrl: attachments) {
//                                    attachmentList.add(CommUtils.parseSrc(attachmentUrl, titleUrl));
//                                }
//                            }
//                            List<String> videos = document.select("video").eachAttr("src");
//                            if (!videos.isEmpty()) {
//                                for(String videoUrl: videos) {
//                                    veidoList.add(CommUtils.parseSrc(videoUrl, titleUrl));
//                                }
//                            }
//                        }
                        jsonObject.setImgList(im);
//                        jsonObject.setAttachmentList(attachmentList);
//                        jsonObject.setVideoList(veidoList);
                        jsonObject.setCallBackUrl(null);
                        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        LocalDateTime ldt = LocalDateTime.parse(jsonObject.getScreenTime().format(df),df);
                        jsonObject.setScreenTime(ldt);
                        for(LookBackUrlList lul: lookBackUrlList){
                            jsonObject.setDataId(lul.getLulExternalId());
                            System.out.println(jsonObject.getId()+lookBackTask.getLbtDescr());
                            log.info(JSONObject.toJSONString(jsonObject));
                            List<IdUrlModel> idUrlModels=new ArrayList<>();
                            IdUrlModel idUrlModel=new IdUrlModel();
                            idUrlModel.setId(jsonObject.getDataId());
                            idUrlModel.setUrl(jsonObject.getHitUrl());
                            idUrlModels.add(idUrlModel);
//                            String postJson=OkHttpUtils.httpPostJson(convertPdfUrl,JSONObject.toJSONString(idUrlModels));
//                            System.out.println("请求html转换返回数据:"+postJson);

                            //boolean flag = true;
                            boolean defaultUrl = false;
                            String url = lookBackTask.getLbtCallbackUrl();
                            String content = JSONObject.toJSONString(jsonObject);
                            String datas = null;
                            if(StringUtils.isNotEmpty(url)){
                                //调用回调pushData：定时任务
                                //String datas=OkHttpUtils.httpPostJson(lookBackTask.getLbtCallbackUrl(), JSONObject.toJSONString(jsonObject));
                                datas = tokenBucketUtils.throttlingCallBack(url, content);
                            }else{
                                defaultUrl = true;
                                url = internalSystemAddress+"api/third/patrolAudit";
                                datas = OkHttpUtils.httpPostJson(url, content);
                            }
                            if (StringUtils.isEmpty(datas) || (!datas.contains("\"code\":200") && !datas.contains("\"code\":500200") && !datas.contains("\"code\":500203"))) {
                                //发送至mq
                                PushDateDTO pushDateDTO = new PushDateDTO();
                                pushDateDTO.setUrl(url);
                                pushDateDTO.setContent(content);
                                pushDateDTO.setDefaultUrl(defaultUrl);
                                lul.setLulState(1);
                                pushDateDTO.setLookBackUrlList(lul);
                                if(lookBackUrlListService.getFinishStateByTaskSid(lookBackTask.getSid())){
                                    lookBackTask.setLbtState(1);
                                    pushDateDTO.setLookBackTask(lookBackTask);
                                }
                                rabbitTemplate.convertAndSend(directFcb, keyFcb, JSONObject.toJSONString(pushDateDTO));
                                log.warn("推送回查数据失败：{}", datas);
                            }else {
                                log.info("推送回查数据成功：{}", content);
                                lul.setLulState(1);
                                lookBackUrlListService.updateEntity(lul);
                                if(lookBackUrlListService.getFinishStateByTaskSid(lookBackTask.getSid())){
                                    lookBackTask.setLbtState(1);
                                    lookBackTaskService.updateEntity(lookBackTask);
                                }
                            }
                        }

//                                    String loginUser=elasticsearchTemplate.insert(indexName, "id", jsonObject);
//                                    System.out.println(loginUser);
                    }

                    String string=OkHttpUtils.httpPostJson(url+"api/notexportdata/update?taskId="+taskId, getBZYToken.getToken(), "{}");
                    System.out.println(string);


                }
            }
        }catch (Exception e){
            log.error("回查推送错误：" + e.getMessage());
            System.out.println(e.getMessage());
            redisUtils.deleteByPrex(taskId);
        }
        redisUtils.deleteByPrex(taskId);
    }

    /**
     * 八爪鱼数据推送：
     * @param getBZYToken
     * @param taskInfo
     * @param taskId
     * @param modelName
     * @throws IOException
     */
    private void pushDate( GetBZYToken getBZYToken, TaskInfo taskInfo,String taskId ,String modelName) throws IOException {
            redisUtils.set(taskId, taskInfo.getSid());
            try {
                //根据任务id查询数据
                String s1=OkHttpUtils.httpGet(url+"api/notexportdata/gettop?taskId="+ taskId+"&size=1000", getBZYToken.getToken());
                JSONObject jsonObject1 =JSON.parseObject(s1);
                if("success".equals(jsonObject1.getString("error"))){
                    JSONObject data=jsonObject1.getJSONObject("data");
                    JSONArray jsonArray1 =data.getJSONArray("dataList");
                    if(ObjectUtils.isNotEmpty(jsonArray1)){
                        TaskInfoUpdateReq request=new TaskInfoUpdateReq();
                        request.setSid(taskInfo.getSid());
                        request.setTiRunNum(taskInfo.getTiRunNum()+1);
                        CommonResult commonResult1=httpApiService.update(request);
                        log.info(JSONObject.toJSONString(commonResult1));
                        for (int k = 0; k < jsonArray1.size(); k++) {
                            JSONObject jsonObject2=jsonArray1.getJSONObject(k);
                            log.info("从八爪鱼获取到的数据"+jsonObject2);
                            String title=jsonObject2.getString("标题");
                            String titleUrl=jsonObject2.getString("标题链接");
                            String time=jsonObject2.getString("时间");
                            String html=jsonObject2.containsKey("html") ? jsonObject2.getString("html") : null;
                            jsonObject2.remove("标题");
                            jsonObject2.remove("标题链接");
                            jsonObject2.remove("时间");
                            if(Objects.nonNull(html)){
                                jsonObject2.remove("html");
                            }
                            Map<String, String> jsonMap = JSONObject.toJavaObject(jsonObject2, Map.class);
                            ESModel jsonObject=new ESModel();
    //                        jsonObject.setCompanyId(taskInfo.getTiCpiSid());
                            jsonObject.setId(taskInfo.getSid());
                            jsonObject.setProjectId(null);
                            jsonObject.setDataId(UUID.randomUUID().toString().trim().replaceAll("-", ""));
                            jsonObject.setTitle(title);
                            jsonObject.setAppId(null);
                            jsonObject.setTaskId(taskInfo.getTiExternalId());
                            jsonObject.setCaseId(null);
                            jsonObject.setHitUrl(titleUrl);
                            if(StringUtils.isNotEmpty(time)){
                                jsonObject.setScreenTime(ifTime(time));
                            }else {
                                DateTimeFormatter dfg = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                                String string = new SimpleDateFormat("yyyy-MM-dd").format(new Date()).toString();
                                String time1 = new SimpleDateFormat("HH:mm:ss").format(new Date()).toString();
                                jsonObject.setScreenTime(LocalDateTime.parse(string+" "+time1,dfg));
                            }
                            jsonObject.setEventId(null);
                            jsonObject.setSearchWordId(null);
                            jsonObject.setModuleName(modelName);
                            jsonObject.setTypeCode(taskInfo.getTiType());
                            jsonObject.setReadTheNumber("0");
                            jsonObject.setThumbUpFor("0");
                            jsonObject.setLookingAtTheNumber("0");
                            jsonObject.setForwardingNumber("0");
                            jsonObject.setComments("0");
                            List<ESModelDetail> esModelDetails=new ArrayList<>();
                            List<String> im=new ArrayList<>();
                            StringBuffer stringBuffer = new StringBuffer();
                            for (String key : jsonMap.keySet()) {
                                String value = jsonMap.get(key);
                                if(isHttpUrl(value)){
                                    for (int l = 0; l < listImg.size(); l++) {
                                        if(value.indexOf(listImg.get(l))>=0){
                                            im.add(value);
                                        }
    //                                    else{
    //                                        jsonObject.setHitUrl(value);
    //                                    }
                                    }

                                }else{
                                    ESModelDetail esModelDetail=new ESModelDetail();
                                    esModelDetail.setWord(value);
                                    Position position=new Position();
                                    esModelDetail.setPosition(position);
                                    esModelDetails.add(esModelDetail);
                                    if(StringUtils.isNotEmpty(value)){
                                        value=replaceBlank(value);
                                    }
                                    stringBuffer.append(value);
                                }
                            }
                            jsonObject.setText(stringBuffer.toString());
                            jsonObject.setEsModelDetailList(esModelDetails);
    //                        List<String> attachmentList=new ArrayList<>();
    //                        List<String> veidoList=new ArrayList<>();
    //                        if (html != null) {
    //                            Document document = Jsoup.parse(html);
    //                            List<String> imgs = document.select("img").eachAttr("src");
    //                            if(!imgs.isEmpty()){
    //                                for(String imgUrl: imgs) {
    //                                    im.add(CommUtils.parseSrc(imgUrl, titleUrl));
    //                                }
    //                            }
    //                            List<String> attachments = document.select("a").eachAttr("href");
    //                            if (!attachments.isEmpty()) {
    //                                for(String attachmentUrl: attachments) {
    //                                    attachmentList.add(CommUtils.parseSrc(attachmentUrl, titleUrl));
    //                                }
    //                            }
    //                            List<String> videos = document.select("video").eachAttr("src");
    //                            if (!videos.isEmpty()) {
    //                                for(String videoUrl: videos) {
    //                                    veidoList.add(CommUtils.parseSrc(videoUrl, titleUrl));
    //                                }
    //                            }
    //                        }
                            jsonObject.setImgList(im);
    //                        jsonObject.setAttachmentList(attachmentList);
    //                        jsonObject.setVideoList(veidoList);
                            jsonObject.setCallBackUrl(null);
                            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            LocalDateTime ldt = LocalDateTime.parse(jsonObject.getScreenTime().format(df),df);
                            jsonObject.setScreenTime(ldt);
                            System.out.println(jsonObject.getId()+taskInfo.getTiDescr());
                            log.info(JSONObject.toJSONString(jsonObject));
                            List<IdUrlModel> idUrlModels=new ArrayList<>();
                            IdUrlModel idUrlModel=new IdUrlModel();
                            idUrlModel.setId(jsonObject.getDataId());
                            idUrlModel.setUrl(jsonObject.getHitUrl());
                            idUrlModels.add(idUrlModel);
//                            String postJson=OkHttpUtils.httpPostJson(convertPdfUrl,JSONObject.toJSONString(idUrlModels));
//                            System.out.println("请求html转换返回数据:"+postJson);

                            boolean defaultUrl = false;
                            String url = taskInfo.getTiExternalCallBackUrl();
                            String content = JSONObject.toJSONString(jsonObject);
                            String datas = null;
                            if(StringUtils.isNotEmpty(url)){
                                //调用回调pushData：定时任务
                                //String datas=OkHttpUtils.httpPostJson(taskInfo.getTiExternalCallBackUrl(), JSONObject.toJSONString(jsonObject));
                                datas = tokenBucketUtils.throttlingCallBack(url, content);
                            }else{
                                url = internalSystemAddress+"api/third/patrolAudit";
                                datas=OkHttpUtils.httpPostJson(url, content);
                                defaultUrl = true;
                            }
                            if (StringUtils.isEmpty(datas) || (!datas.contains("\"code\":200") && !datas.contains("\"code\":500200") && !datas.contains("\"code\":500203"))) {
                                //发送至mq
                                PushDateDTO pushDateDTO = new PushDateDTO();
                                pushDateDTO.setUrl(url);
                                pushDateDTO.setContent(content);
                                pushDateDTO.setDefaultUrl(defaultUrl);
                                rabbitTemplate.convertAndSend(directFcb, keyFcb, JSONObject.toJSONString(pushDateDTO));
                                log.warn("推送巡查数据失败：{}" , datas);
                            }else {
                                log.info("推送巡查数据成功：{}" , content);
                            }
    //                                    String loginUser=elasticsearchTemplate.insert(indexName, "id", jsonObject);
    //                                    System.out.println(loginUser);
                        }

                        String string=OkHttpUtils.httpPostJson(url+"api/notexportdata/update?taskId="+taskId, getBZYToken.getToken(), "{}");
                        System.out.println(string);
                    }
                }
            }catch (Exception e){
                redisUtils.deleteByPrex(taskId);
            }
        redisUtils.deleteByPrex(taskId);
    }

    /**
     * 更新任务状态（废弃）
     */
//    @Scheduled(cron = "0 0 0 * * ?")
    public void scheduledByUpdateStatus() throws IOException {
        GetBZYToken getBZYToken=new GetBZYToken(redisUtils);
        String taskGroupData=OkHttpUtils.httpGet(url+"api/TaskGroup", getBZYToken.getToken());
        JSONArray jsonArray=getBZYData(taskGroupData);
        for (int i = 0; i < jsonArray.size(); i++) {
            String taskData=OkHttpUtils.httpGet(url+"api/Task?taskGroupId="+jsonArray.getJSONObject(i).getInteger("taskGroupId"), getBZYToken.getToken());
            JSONArray jsonArray1=getBZYData(taskData);
            List<String> ls=new ArrayList<>();
            for (int j = 0; j < jsonArray1.size(); j++) {
                ls.add(jsonArray1.getJSONObject(j).getString("taskId"));
            }
            JSONObject parm=new JSONObject();
            parm.put("taskIdList", ls);
            JSONArray jsonArray2=new JSONArray();
            if(ls.size()>0){
                String getTaskStatusByIdList=OkHttpUtils.httpPostJson(url+"api/task/GetTaskStatusByIdList", getBZYToken.getToken(), parm.toJSONString());
                jsonArray2= getBZYData(getTaskStatusByIdList);
            }
            CommonResult commonResult=httpApiService.listTaskByOrder(new TaskInfoSearchReq());
            if(200==commonResult.getCode()){
                if(ObjectUtils.isNotEmpty(commonResult.getData())){
                    List<TaskInfo> list= (List<TaskInfo>) commonResult.getData();
                    for (int j = 0; j < jsonArray2.size(); j++) {
                        JSONObject jsonObject2=jsonArray2.getJSONObject(j);
                        if(jsonObject2.getInteger("status").equals(0)){
                            for (int k = 0; k < list.size(); k++) {
                                TaskInfo taskInfo=list.get(k);
                                if(jsonObject2.getString("taskName").indexOf(taskInfo.getTiSno())>=0){
                                    String s1=OkHttpUtils.httpPostJson("api/task/StopTask?taskId="+jsonObject2.getString("taskId"), getBZYToken.getToken(), "{}");
                                    TaskInfoUpdateReq request=new TaskInfoUpdateReq();
                                    request.setSid(taskInfo.getSid());
                                    request.setTiStatus(EntityTypeEnum.TaskStatusTypeEnum.Stop.getCode());
                                    CommonResult commonResult1=httpApiService.update(request);
                                    log.info("=====>>>>>关闭信息  {}",s1);
                                    log.info("=====>>>>>修改任务状态信息  {}",JSONObject.toJSONString(commonResult1));
                                }
                            }
                        }
                    }

                }
            }


        }

        log.info("=====>>>>>使用cron  {}",System.currentTimeMillis());
    }

    public String run(String params){
        JSONObject jsonObject=new JSONObject();
        jsonObject.put("success", true);
        ESModel esModel=JSONObject.parseObject(params, ESModel.class);

        TaskInfoSearchReq taskInfoSearchReq=new TaskInfoSearchReq();
        List<Integer> typeList=new ArrayList<>();
        typeList.add(302105);
        typeList.add(302106);
        taskInfoSearchReq.setTypeList(typeList);
        taskInfoSearchReq.setTiExternalId(esModel.getTaskId());
        CommonResult commonResult=httpApiService.list(taskInfoSearchReq);
        List<TaskInfo> list=new ArrayList<>();
        if(200==commonResult.getCode()){
            if(ObjectUtils.isNotEmpty(commonResult.getData())){
                list= (List<TaskInfo>) commonResult.getData();
            }
        }

        if (list.size()>0){
            String startDate="";
            String endDate="";
            //如果使用时间段格式示例 20220330120000
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            SimpleDateFormat sdfx = new SimpleDateFormat("yyyyMMdd");
            Calendar c = Calendar.getInstance();
            c.add(Calendar.DAY_OF_MONTH, -100);
            Date finalDate = c.getTime();
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            Date calendarTime = calendar.getTime();
            TaskInfo taskInfo=JSONObject.parseObject(JSONObject.toJSONString(list.get(0)), TaskInfo.class);
            TaskInfoUpdateReq request=new TaskInfoUpdateReq();
            request.setSid(taskInfo.getSid());
            request.setTiRunNum(taskInfo.getTiRunNum()+1);
            CommonResult commonResult1=httpApiService.update(request);
            log.info(JSONObject.toJSONString(commonResult1));
            if(taskInfo.getTiRunNum().equals(0)){
                endDate=sdfx.format(new Date());
                if(taskInfo.getTiTypeChild()==3){
                    startDate=sdf.format(finalDate);
                }
                if(taskInfo.getTiTypeChild()==2){
                    startDate=sdf.format(finalDate);
                }
                if(taskInfo.getTiTypeChild()==1){
                    startDate=sdf.format(calendarTime);
                }
            }else{
                if(taskInfo.getTiTypeChild()!=2) {
                    endDate = sdfx.format(new Date());
                    startDate = sdf.format(calendarTime);
                }
            }
            int pageNum=1;
            int pageSize=1000;
            if(StringUtils.isNotEmpty(startDate)){
                //微博
                if(esModel.getSearchWordId().equals("300308")){
                    getWeiboData(pageNum,pageSize,startDate, endDate, esModel.getModuleName(),esModel);
                }
                //公众号
                if(esModel.getSearchWordId().equals("300303")){
                    getWeiXinData(pageNum,pageSize,startDate, endDate, esModel.getModuleName(),esModel);
                }
            }
        }
        esModel.setId(CommUtils.ShortUUID());
        log.info("=====>>>>>使用动态cron  {}",System.currentTimeMillis()+"-"+JSONObject.toJSONString(esModel));
        return jsonObject.toJSONString();
    }


    public void  getWeiboData(int pageNum,int pageSize,String startDate,String endDate,String modelName,ESModel esModel ){
        String s=OkHttpUtils.postDataByForm(thirdPartyPlatformAddress+"weibo/article", "query=IR_LOADTIME:["+startDate+"  TO "+endDate+"  ] AND IR_SCREEN_NAME:"+modelName+"&pageNo="+pageNum+"&pageSize="+pageSize, null);
        JSONObject jsonObject1=JSON.parseObject(s);
        if(("yes").equals(jsonObject1.getString("existingData"))){
            JSONArray jsonArray1 =jsonObject1.getJSONArray("msg");
            if(ObjectUtils.isNotEmpty(jsonArray1)){
                //解析数据
                for (int i = 0; i < jsonArray1.size(); i++) {
                    JSONObject jsonObject=jsonArray1.getJSONObject(i);
                    Map<String, String> jsonMap = JSONObject.toJavaObject(jsonObject, Map.class);
                    ESModel esModel1=new ESModel();
                    BeanUtils.copyProperties(esModel, esModel1);
                    esModel1.setHitUrl(jsonObject.getString("IR_URLNAME"));
                    esModel1.setId(CommUtils.ShortUUID());
                    DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
                    LocalDateTime ldt = LocalDateTime.parse(jsonObject.getString("IR_LOADTIME"),df);
                    esModel1.setScreenTime(ldt);
                    esModel1.setReadTheNumber("0");
                    esModel1.setThumbUpFor("0");
                    esModel1.setLookingAtTheNumber("0");
                    esModel1.setForwardingNumber("0");
                    esModel1.setComments("0");
                    esModel1.setTitle(jsonObject.getString("IR_STATUS_CONTENT"));
                    List<ESModelDetail> esModelDetails=new ArrayList<>();
                    List<String> im=new ArrayList<>();
                    String image=jsonObject.getString("IR_OCR_IMAGE");
                    if(StringUtils.isNotEmpty(image)){
                        String[] split=image.split(";");
                        im.addAll(Arrays.asList(split));
                    }
                    StringBuffer stringBuffer = new StringBuffer();
                    for (String key : jsonMap.keySet()) {
                        String value = jsonMap.get(key);
                        ESModelDetail esModelDetail=new ESModelDetail();
                        esModelDetail.setWord(value);

                        Position position=new Position();
                        esModelDetail.setPosition(position);
                        esModelDetails.add(esModelDetail);
                        stringBuffer.append(value);
                    }
                    esModel1.setText(stringBuffer.toString());
                    esModel1.setEsModelDetailList(esModelDetails);
                    esModel1.setImgList(im);
                    esModel1.setCallBackUrl(null);

                    boolean defaultUrl = false;
                    String url = esModel.getCallBackUrl();
                    String content = JSONObject.toJSONString(esModel1);
                    String datas = null;
                    if(StringUtils.isNotEmpty(url)){
                        //调用回调pushData：定时任务
                        //String datas=OkHttpUtils.httpPostJson(esModel.getCallBackUrl(), JSONObject.toJSONString(esModel1));
                        datas = tokenBucketUtils.throttlingCallBack(url, content);
                    }else{
                        defaultUrl = true;
                        url = internalSystemAddress+"api/third/patrolAudit";
                        datas = OkHttpUtils.httpPostJson(url, content);
                    }
                    if (StringUtils.isEmpty(datas) || (!datas.contains("\"code\":200") && !datas.contains("\"code\":500200") && !datas.contains("\"code\":500203"))) {
                        //发送至mq
                        PushDateDTO pushDateDTO = new PushDateDTO();
                        pushDateDTO.setUrl(url);
                        pushDateDTO.setContent(content);
                        pushDateDTO.setDefaultUrl(defaultUrl);
                        rabbitTemplate.convertAndSend(directFcb, keyFcb, JSONObject.toJSONString(pushDateDTO));
                        log.warn("推送巡查数据失败：{}" , datas);
                    }else {
                        log.info("推送巡查数据成功：{}" , content);
                    }

//                    log.info("推送的微博数据"+JSONObject.toJSONString(esModel1));
                }
            }

            //处理翻页
            Integer totalSize =jsonObject1.getInteger("totalSize");
            Integer size=totalSize-(pageNum*pageSize);
            BigDecimal b = new BigDecimal((float)size/pageSize);
            Double result = b.setScale(3, BigDecimal.ROUND_HALF_UP).doubleValue();
            if(Math.ceil(result)>=1){
                getWeiboData(pageNum+1,pageSize, startDate, endDate, modelName,esModel);
            }
        }else{
            log.error("加载微博数据失败"+s);
        }
    }
    public void  getWeiXinData(int pageNum,int pageSize,String startDate,String endDate,String modelName,ESModel esModel ){
        String s=OkHttpUtils.postDataByForm(thirdPartyPlatformAddress+"weixin/article", "query=IR_LOADTIME:["+startDate+"  TO "+endDate+"  ] AND IR_SITENAME:"+modelName+"&pageNo="+pageNum+"&pageSize="+pageSize, null);
        JSONObject jsonObject1=JSON.parseObject(s);
        if(("yes").equals(jsonObject1.getString("existingData"))){
            JSONArray jsonArray1 =jsonObject1.getJSONArray("msg");
            if(ObjectUtils.isNotEmpty(jsonArray1)){
                //解析数据
                for (int i = 0; i < jsonArray1.size(); i++) {
                    JSONObject jsonObject=jsonArray1.getJSONObject(i);
                    Map<String, String> jsonMap = JSONObject.toJavaObject(jsonObject, Map.class);
                    ESModel esModel1=new ESModel();
                    BeanUtils.copyProperties(esModel, esModel1);
                    esModel1.setHitUrl(jsonObject.getString("IR_URLNAME"));
                    esModel1.setId(CommUtils.ShortUUID());
                    DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
                    LocalDateTime ldt = LocalDateTime.parse(jsonObject.getString("IR_LOADTIME"),df);
                    esModel1.setScreenTime(ldt);
                    esModel1.setReadTheNumber("0");
                    esModel1.setThumbUpFor("0");
                    esModel1.setLookingAtTheNumber("0");
                    esModel1.setForwardingNumber("0");
                    esModel1.setComments("0");
                    esModel1.setTitle(jsonObject.getString("IR_URLTITLE"));
                    List<ESModelDetail> esModelDetails=new ArrayList<>();
                    List<String> im=new ArrayList<>();
                   //自定义处理图片
                    StringBuffer stringBuffer = new StringBuffer();
                    for (String key : jsonMap.keySet()) {
                        String value = jsonMap.get(key);
                        String regex = "SRC=\"(.*?)\">;";
                        Matcher matcher = Pattern.compile(regex).matcher(value);
                        while (matcher.find()) {
                            String img = matcher.group(1);
                            im.add(img);
                            value=value.replaceAll(img, "");
                        }
                        ESModelDetail esModelDetail=new ESModelDetail();
                        esModelDetail.setWord(value);
                        Position position=new Position();
                        esModelDetail.setPosition(position);
                        esModelDetails.add(esModelDetail);
                        stringBuffer.append(value);
                    }
                    esModel1.setText(stringBuffer.toString());
                    esModel1.setEsModelDetailList(esModelDetails);
                    esModel1.setImgList(im);
                    esModel1.setCallBackUrl(null);

                    boolean defaultUrl = false;
                    String url = esModel.getCallBackUrl();
                    String content = JSONObject.toJSONString(esModel1);
                    String datas = null;
                    if(StringUtils.isNotEmpty(url)){
                        //调用回调pushData：定时任务
                        //String datas=OkHttpUtils.httpPostJson(esModel.getCallBackUrl(), JSONObject.toJSONString(esModel1));
                        datas = tokenBucketUtils.throttlingCallBack(url, content);
                    }else{
                        defaultUrl = true;
                        url = internalSystemAddress+"api/third/patrolAudit";
                        datas=OkHttpUtils.httpPostJson(url, content);
                    }
                    if (StringUtils.isEmpty(datas) || (!datas.contains("\"code\":200") && !datas.contains("\"code\":500200") && !datas.contains("\"code\":500203"))) {
                        //发送至mq
                        PushDateDTO pushDateDTO = new PushDateDTO();
                        pushDateDTO.setUrl(url);
                        pushDateDTO.setContent(content);
                        pushDateDTO.setDefaultUrl(defaultUrl);
                        rabbitTemplate.convertAndSend(directFcb, keyFcb, JSONObject.toJSONString(pushDateDTO));
                        log.warn("推送巡查数据失败：{}" , datas);
                    }else {
                        log.info("推送巡查数据成功：{}" , content);
                    }
//                    log.info("推送的微信数据"+JSONObject.toJSONString(esModel1));
                }
            }

            //处理翻页
            Integer totalSize =jsonObject1.getInteger("totalSize");
            Integer size=totalSize-(pageNum*pageSize);
            BigDecimal b = new BigDecimal((float)size/pageSize);
            Double result = b.setScale(3,BigDecimal.ROUND_HALF_UP).doubleValue();
            if(Math.ceil(result)>=1){
                getWeiXinData(pageNum+1,pageSize, startDate, endDate, modelName,esModel);
            }
        }else{
            log.error("加载微信数据失败"+s);
        }
    }
    private JSONArray getBZYData(String getTaskStatusByIdList) {
        System.out.println("返回数据"+getTaskStatusByIdList);
        JSONArray jsonArray =new JSONArray();
        JSONObject getTaskStatusByIdListOBj = JSON.parseObject(getTaskStatusByIdList);
        if ("success".equals(getTaskStatusByIdListOBj.getString("error"))) {
            jsonArray = getTaskStatusByIdListOBj.getJSONArray("data");
        }
        return jsonArray;
    }

    /**
     * 判断字符串是否为URL
     * @param urls 需要判断的String类型url
     * @return true:是URL；false:不是URL
     */
    public static boolean isHttpUrl(String urls) {
        boolean isurl = false;
        String regex = "^([hH][tT]{2}[pP]://|[hH][tT]{2}[pP][sS]://)(([A-Za-z0-9-~]+).)+([A-Za-z0-9-~\\/])+$";//设置正则表达式

        Pattern pat = Pattern.compile(regex.trim());//对比
        Matcher mat = pat.matcher(urls.trim());
        isurl = mat.matches();//判断是否匹配
        if (isurl) {
            isurl = true;
        }
        return isurl;
    }

    public static LocalDateTime ifTime(String ss){
        DateTimeFormatter dfg = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter dfd = DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm:ss");
        DateTimeFormatter dfxg = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
        String string = new SimpleDateFormat("yyyy-MM-dd").format(new Date()).toString();
        String time = new SimpleDateFormat("HH:mm:ss").format(new Date()).toString();
        String regex="[0-9]{4}[-,.,/][0-9]{2}[-,.,/][0-9]{2}";
        Pattern pattern = Pattern.compile(regex.trim());
        Matcher matcher = pattern.matcher(ss);
        while(matcher.find()){
            System.out.println(matcher.group());
            try{
                if(ss.indexOf(".")>=0){
                    return LocalDateTime.parse(matcher.group()+" 00:00:00",dfd);
                }else
                if(ss.indexOf("/")>=0){
                    return LocalDateTime.parse(matcher.group()+" 00:00:00",dfxg);
                }else
                if(ss.indexOf("-")>=0){
                    return LocalDateTime.parse(matcher.group()+" 00:00:00",dfg);
                }else {
                    return LocalDateTime.parse(string+" "+time,dfg);
                }
            }catch (Exception e){
                return LocalDateTime.parse(string+" "+time,dfg);
            }
        }
        return LocalDateTime.parse(string+" "+time,dfg);
    }
    public static String replaceBlank(String str) {
        String dest = "";

        if (str != null) {
            Pattern p = Pattern.compile("\\s*|\t|\r|\n");

            Matcher m = p.matcher(str);

            dest = m.replaceAll("");

        }

        return dest;

    }
}
