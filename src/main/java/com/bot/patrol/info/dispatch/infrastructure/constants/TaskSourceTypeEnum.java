package com.bot.patrol.info.dispatch.infrastructure.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TaskSourceTypeEnum {
    ALPHA_TESTING(302101, "内部测试"),
    PEOPLE_CN(302102, "人民网巡查业务"),
    APP_PATROL(302103, "APP巡查业务"),
    ANNEXED_TERRITORY(302104, "属地巡查业务");


    private final int code;
    private final String value;

    //根据key获取枚举
    public static TaskSourceTypeEnum getEnumByKey(int key){
        for(TaskSourceTypeEnum temp: TaskSourceTypeEnum.values()){
            if(temp.getCode() == key){
                return temp;
            }
        }
        return null;
    }
}