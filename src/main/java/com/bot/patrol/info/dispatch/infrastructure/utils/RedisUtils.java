package com.bot.patrol.info.dispatch.infrastructure.utils;

import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
@AllArgsConstructor
public class RedisUtils {

    public final RedisTemplate redisTemplate;

    public <T> void set(final String key, final T value) {
        redisTemplate.opsForValue().set(key, value);
    }
    public void deleteByPrex(String prex) {
        Set<String> keys = redisTemplate.keys(prex);
        if (CollectionUtils.isNotEmpty(keys)) {
            redisTemplate.delete(keys);
        }
    }

    public <T> void set(final String key, final T value, final Integer timeout, final TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
    }

    public <T> T get(final String key) {
        ValueOperations<String, T> operation = redisTemplate.opsForValue();
        return operation.get(key);
    }

    public <T> void setList(final String key, final String value) {
        redisTemplate.opsForList().leftPush(key, value);

    }

    public <T> List<String> getList(final String key) {
       return (List<String>) redisTemplate.opsForList().leftPop(key);
    }

    public <T> void updateList(final String key,final String oldValue,final String value) {
        List<String> stringList=(List<String>) redisTemplate.opsForList().range(key, 0, -1);
        for (int i = 0; i < stringList.size(); i++) {
            if(stringList.get(i).equals(oldValue)){
                redisTemplate.opsForList().set(key,i,value);
            }
        }
    }

    public <T> void deleteList(final String key,final String value) {
        redisTemplate.opsForList().remove(key, 0, value);
    }
}
