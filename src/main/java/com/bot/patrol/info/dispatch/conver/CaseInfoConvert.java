package com.bot.patrol.info.dispatch.conver;


import com.bot.patrol.info.dispatch.entity.CaseInfo;
import com.bot.patrol.info.dispatch.model.req.CaseInfoUpdateReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.Map;

@Mapper
public interface CaseInfoConvert {
    CaseInfoConvert INSTANCE = Mappers.getMapper(CaseInfoConvert.class);
    @Mappings({
            @Mapping(target = "sid", expression = "java((String)request.get(\"sid\"))"),
            @Mapping(target = "ciSno", expression = "java((String)request.get(\"ciSno\"))"),
            @Mapping(target = "ciCaseName", expression = "java((String)request.get(\"ciCaseName\"))"),
            @Mapping(target = "ciIndustryType", expression = "java((String)request.get(\"ciIndustryType\"))"),
            @Mapping(target = "ciPiSid", expression = "java((String)request.get(\"ciPiSid\"))"),
            @Mapping(target = "ciCpiSid", expression = "java((String)request.get(\"ciCpiSid\"))"),
            @Mapping(target = "ciCpiName", expression = "java((String)request.get(\"ciCpiName\"))"),
            @Mapping(target = "ciTiSid", expression = "java((String)request.get(\"ciTiSid\"))"),
            @Mapping(target = "ciTiSno", expression = "java((String)request.get(\"ciTiSno\"))"),
            @Mapping(target = "ciTiType", expression = "java((Integer)request.get(\"ciTiType\"))"),
            @Mapping(target = "ciOlSid", expression = "java((String)request.get(\"ciOlSid\"))"),
            @Mapping(target = "ciOlSno", expression = "java((String)request.get(\"ciOlSno\"))"),
            @Mapping(target = "ciSiSid", expression = "java((String)request.get(\"ciSiSid\"))"),
            @Mapping(target = "ciSiType", expression = "java((Integer)request.get(\"ciSiType\"))"),
            @Mapping(target = "ciSteps", expression = "java((String)request.get(\"ciSteps\"))"),
            @Mapping(target = "ciAiSid", expression = "java((String)request.get(\"ciAiSid\"))"),
            @Mapping(target = "ciAiPackage", expression = "java((String)request.get(\"ciAiPackage\"))"),
            @Mapping(target = "ciAiType", expression = "java((Integer)request.get(\"ciAiType\"))"),
            @Mapping(target = "ciAiName", expression = "java((String)request.get(\"ciAiName\"))"),
            @Mapping(target = "ciAiGetOrigin", expression = "java((String)request.get(\"ciAiGetOrigin\"))"),
            @Mapping(target = "ciRunType", expression = "java((Integer)request.get(\"ciRunType\"))"),
            @Mapping(target = "ciRunCycle", expression = "java((Integer)request.get(\"ciRunCycle\"))"),
            @Mapping(target = "ciCycleNum", expression = "java((Integer)request.get(\"ciCycleNum\"))"),
            @Mapping(target = "ciAciSid", expression = "java((String)request.get(\"ciAciSid\"))"),
            @Mapping(target = "ciRuntimeAciSid", expression = "java((String)request.get(\"ciRuntimeAciSid\"))"),
            @Mapping(target = "ciAciName", expression = "java((String)request.get(\"ciAciName\"))"),
            @Mapping(target = "ciAciQueueFlag", expression = "java((String)request.get(\"ciAciQueueFlag\"))"),
            @Mapping(target = "ciAciConnType", expression = "java((Integer)request.get(\"ciAciConnType\"))"),
            @Mapping(target = "ciAciDevice", expression = "java((String)request.get(\"ciAciDevice\"))"),
            @Mapping(target = "ciStartTime", expression = "java(java.util.Objects.isNull(request.get(\"ciStartTime\")) ? null : java.time.LocalDateTime.parse((String)request.get(\"ciStartTime\"), java.time.format.DateTimeFormatter.ofPattern(\"yyyy-MM-dd HH:mm:ss\")))"),
            @Mapping(target = "ciEndTime", expression = "java(java.util.Objects.isNull(request.get(\"ciEndTime\")) ? null : java.time.LocalDateTime.parse((String)request.get(\"ciEndTime\"), java.time.format.DateTimeFormatter.ofPattern(\"yyyy-MM-dd HH:mm:ss\")))"),
            @Mapping(target = "ciAciDeviceWifi", expression = "java((String)request.get(\"ciAciDeviceWifi\"))"),
            @Mapping(target = "ciOperatorSid", expression = "java((String)request.get(\"ciOperatorSid\"))"),
            @Mapping(target = "ciCreateTime", expression = "java(java.util.Objects.isNull(request.get(\"ciCreateTime\")) ? null : java.time.LocalDateTime.parse((String)request.get(\"ciCreateTime\"), java.time.format.DateTimeFormatter.ofPattern(\"yyyy-MM-dd HH:mm:ss\")))"),
            @Mapping(target = "ciUpdateTime", expression = "java(java.util.Objects.isNull(request.get(\"ciUpdateTime\")) ? null : java.time.LocalDateTime.parse((String)request.get(\"ciUpdateTime\"), java.time.format.DateTimeFormatter.ofPattern(\"yyyy-MM-dd HH:mm:ss\")))"),
            @Mapping(target = "ciState", expression = "java((Integer)request.get(\"ciState\"))"),
            @Mapping(target = "ciStatus", expression = "java((Integer)request.get(\"ciStatus\"))"),
            @Mapping(target = "ciTiPriority", expression = "java((Integer)request.get(\"ciTiPriority\"))"),
            @Mapping(target = "ciLastRunStartTime", expression = "java(java.util.Objects.isNull(request.get(\"ciLastRunStartTime\")) ? null : java.time.LocalDateTime.parse((String)request.get(\"ciLastRunStartTime\"), java.time.format.DateTimeFormatter.ofPattern(\"yyyy-MM-dd HH:mm:ss\")))"),
            @Mapping(target = "ciLastRunEndTime", expression = "java(java.util.Objects.isNull(request.get(\"ciLastRunEndTime\")) ? null : java.time.LocalDateTime.parse((String)request.get(\"ciLastRunEndTime\"), java.time.format.DateTimeFormatter.ofPattern(\"yyyy-MM-dd HH:mm:ss\")))"),
            @Mapping(target = "ciAciServiceFlag", expression = "java((String)request.get(\"ciAciServiceFlag\"))")
    })
    CaseInfo convert(Map<String, Object> request);

    @Mappings({})
    CaseInfo convert(CaseInfoUpdateReq request);
}
