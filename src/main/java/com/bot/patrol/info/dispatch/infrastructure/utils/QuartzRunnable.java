package com.bot.patrol.info.dispatch.infrastructure.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;

/**
 * 执行定时任务
 * <AUTHOR>
@Slf4j
public class QuartzRunnable  {

	private Object target;
	private Method method;
	private String params;

	QuartzRunnable(String beanName, String methodName, String params)
			throws NoSuchMethodException, SecurityException {
		this.target = SpringContextHolder.getBean(beanName);
		this.params = params;

		if (StringUtils.isNotBlank(params)) {
			this.method = target.getClass().getDeclaredMethod(methodName, String.class);
		} else {
			this.method = target.getClass().getDeclaredMethod(methodName);
		}
	}

	public String run() {
		try {
			Object o=new Object();
			ReflectionUtils.makeAccessible(method);
			if (StringUtils.isNotBlank(params)) {
				o=method.invoke(target, params);
			} else {
				o=method.invoke(target);
			}
			if(ObjectUtils.isNotEmpty(o)){
				return o.toString();
			}
			JSONObject jsonObject=new JSONObject();
			jsonObject.put("success", true);
			return jsonObject.toJSONString();
		} catch (Exception e) {
			return e.getMessage();
		}
	}

}
