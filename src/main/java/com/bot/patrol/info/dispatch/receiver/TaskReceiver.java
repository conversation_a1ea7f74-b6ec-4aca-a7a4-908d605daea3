package com.bot.patrol.info.dispatch.receiver;

import com.bot.patrol.info.dispatch.service.ITaskService;
import com.rabbitmq.client.Channel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;

@Slf4j
@Component
@AllArgsConstructor

public class TaskReceiver {

    private final ITaskService taskService;
    //private final String pushTaskErrorUrl = CommUtils.getProps("task.console-addr") + "/task-info/push-error";
    private final DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @RabbitListener(
            bindings=@QueueBinding(
                    value=@Queue(value="${customs.task.rabbitmq-task-dispatch-queue}",autoDelete="false"),
                    exchange=@Exchange(value="${customs.task.rabbitmq-task-dispatch-direct}",durable="true", type= ExchangeTypes.DIRECT),
                    key="${customs.task.rabbitmq-task-dispatch-key}"
            ), concurrency = "5", ackMode = "MANUAL"
    )
    @RabbitHandler
    public void manualComplete(Message message, Channel channel) throws IOException {
        log.info("收到任务执行消息:{}", message);
        try {
            String msg = new String(message.getBody(), StandardCharsets.UTF_8);
            JSONObject joMsg = JSONObject.fromObject(msg);
            if (joMsg.getString("type").equals("taskRun")){
                taskService.addSchediuleTask(joMsg.getString("tiSid"));
            }
            if (joMsg.getString("type").equals("taskStop")){
                taskService.stopSchediuleTask(joMsg.getString("tiSid"));
            }
            log.info("任务处理完成:{}", message);

        }catch (Exception e) {
            log.error("任务执行初始化失败:{}", message, e);
        }
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        log.info("任务调度完成:{}", message);
    }
}
