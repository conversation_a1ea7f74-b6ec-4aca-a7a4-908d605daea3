package com.bot.patrol.info.dispatch.infrastructure.utils;

public class KeyUtils {

    private static final String AUTH_TOKEN_KEY_PREFIX = "AUTH_TOKEN:";
    private static final String WEB_TOKEN_KEY_PREFIX = "WEB_TOKEN:";
    private static final String CAPTCHA_KEY_PREFIX = "CAPTCHA:";
    private static final String VER_CODE_KEY_PREFIX = "VER_CODE:";

    public static String getAuthTokenKey(final String key) {
        return AUTH_TOKEN_KEY_PREFIX + key;
    }

    public static String getWebTokenKey(final String key) {
        return WEB_TOKEN_KEY_PREFIX + key;
    }

    public static String getCaptchaKey(final String key) {
        return CAPTCHA_KEY_PREFIX + key;
    }

    public static String getVerCodeKey(final String key) {
        return VER_CODE_KEY_PREFIX + key;
    }
}