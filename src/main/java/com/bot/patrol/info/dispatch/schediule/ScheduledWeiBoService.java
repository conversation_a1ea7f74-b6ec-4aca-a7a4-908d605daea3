package com.bot.patrol.info.dispatch.schediule;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bot.patrol.info.dispatch.entity.TaskInfo;
import com.bot.patrol.info.dispatch.infrastructure.constants.CommonResult;
import com.bot.patrol.info.dispatch.infrastructure.utils.*;
import com.bot.patrol.info.dispatch.model.ESModel;
import com.bot.patrol.info.dispatch.model.ESModelDetail;
import com.bot.patrol.info.dispatch.model.dto.PushDateDTO;
import com.bot.patrol.info.dispatch.model.req.OrderListSearchReq;
import com.bot.patrol.info.dispatch.model.req.TaskInfoUpdateReq;
import com.bot.patrol.info.dispatch.service.HttpApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.awt.*;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.*;

/**
 * 定时任务
 */
@Slf4j
@Component
public class ScheduledWeiBoService {

    @Value("${weiboAddress}")
    private String weiboAddress;
    @Autowired
    public HttpApiService httpApiService;
    @Value("${internalSystemAddress}")
    private String internalSystemAddress;
    @Value("${weboscheduled}")
    private String weboscheduled;


    @Autowired
    private TokenBucketUtils tokenBucketUtils;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Value("${customs.props.customer-direct-fail-callback}")
    private String directFcb;
    @Value("${customs.props.customer-key-fail-callback}")
    private String keyFcb;
    /**
     *获取任务数据
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void scheduledByGetData() throws IOException, InterruptedException, AWTException {
        if("1".equals(weboscheduled)) {
            OrderListSearchReq orderListSearchReq = new OrderListSearchReq();
            CommonResult commonResult = httpApiService.listByWeiBo(orderListSearchReq);
            List<TaskInfo> list = new ArrayList<>();
            if (200 == commonResult.getCode()) {
                if (ObjectUtils.isNotEmpty(commonResult.getData())) {
                    list = (List<TaskInfo>) commonResult.getData();
                }
            }
            for (int i = 0; i < list.size(); i++) {
                TaskInfo taskInfo = JSONObject.parseObject(JSONObject.toJSONString(list.get(i)), TaskInfo.class);
                LocalDateTime nows = LocalDateTime.now();
                String endTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(nows);
                LocalDateTime startTimes = null;
                if (taskInfo.getTiRunCycle() == 305101) {
                    if (taskInfo.getTiRunNum() == 0) {
                        startTimes = nows.minusMonths(3);
                    }
                } else {
                    startTimes = nows.minusMinutes(30);
                }
                if (startTimes != null) {
                    pushData(taskInfo, endTime, startTimes);
                }
            }
            log.info("=====>>>>>使用cron  {}", System.currentTimeMillis());
        }
    }

    @Async
    public void pushData(TaskInfo taskInfo, String endTime, LocalDateTime startTimes) {
        String startTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(startTimes);
        String weiBoId= taskInfo.getTiCrawlerId();
        JSONObject jsonObject=new JSONObject();
        jsonObject.put("id",weiBoId);
        jsonObject.put("startTime",startTime);
        jsonObject.put("endTime", endTime);
        String str=OkHttpUtils.httpPostJson(weiboAddress+"getResult",jsonObject.toJSONString());
        log.info("请求微博数据："+str);
        if(StringUtils.isNotEmpty(str)) {
            JSONObject data = JSONObject.parseObject(str);
            if (data.getInteger("code")==200){
                JSONArray jsonArray=data.getJSONArray("msg");
                if(ObjectUtils.isNotEmpty(jsonArray)){
                    for (int j = 0; j < jsonArray.size(); j++) {
                        JSONObject jsonObject1=jsonArray.getJSONObject(j);
                        pushWeiBoData(jsonObject1, taskInfo);
                        TaskInfoUpdateReq request=new TaskInfoUpdateReq();
                        request.setSid(taskInfo.getSid());
                        request.setTiRunNum(taskInfo.getTiRunNum()+1);
                        CommonResult commonResult1=httpApiService.update(request);
                    }
                }
            }
        }
    }

    /**
     * 推送微博数据
     * @param jsonObjects
     * @param taskInfo
     */
    public void  pushWeiBoData(JSONObject jsonObjects,TaskInfo taskInfo){
        ESModel jsonObject=new ESModel();
        jsonObject.setId(taskInfo.getSid());
        jsonObject.setProjectId(null);
        jsonObject.setDataId(UUID.randomUUID().toString().trim().replaceAll("-", ""));
        jsonObject.setTitle("");
        jsonObject.setAppId(null);
        jsonObject.setTaskId(taskInfo.getTiExternalId());
        jsonObject.setCaseId(null);
        jsonObject.setHitUrl(jsonObjects.getString("articleUrl"));
        LocalDateTime localStartDateTime = LocalDateTime.parse(jsonObjects.getString("publishTime"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        jsonObject.setScreenTime(localStartDateTime);
        jsonObject.setEventId(null);
        jsonObject.setSearchWordId(null);
        jsonObject.setModuleName("");
        jsonObject.setTypeCode(taskInfo.getTiType());
        jsonObject.setReadTheNumber("0");
        jsonObject.setThumbUpFor(jsonObjects.getString("upNum"));
        jsonObject.setLookingAtTheNumber("0");
        jsonObject.setForwardingNumber(jsonObjects.getString("retweetNum"));
        jsonObject.setComments(jsonObjects.getString("commentNum"));
        List<ESModelDetail> esModelDetails=new ArrayList<>();
        List<String> im=new ArrayList<>();
        String originalPictures=jsonObjects.getString("originalPictures");
        if(StringUtils.isNotEmpty(originalPictures)){
            String[] imgUrl=originalPictures.split(",");
            for (int i = 0; i <imgUrl.length ; i++) {
                if(!imgUrl[i].equals("无")){
                    im.add(imgUrl[i]);
                }
            }
        }
        jsonObject.setImgList(im);
//        List<String> veidosList = new ArrayList<>();
//        String videos = jsonObjects.getString("vedioUrl");
//        if(StringUtils.isNotEmpty(videos)){
//            String regex = "http.*?video";
//            Pattern pattern = Pattern.compile(regex);
//            Matcher matcher = pattern.matcher(videos);
//            while (matcher.find()) {
//                // 获取匹配到的子字符串
//                String match = matcher.group();
//                veidosList.add(match);
//            }
//        }
//        jsonObject.setVideoList(veidosList);
        jsonObject.setText(jsonObjects.getString("content"));
        jsonObject.setEsModelDetailList(esModelDetails);
        jsonObject.setCallBackUrl(null);
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime ldt = LocalDateTime.parse(jsonObject.getScreenTime().format(df),df);
        jsonObject.setScreenTime(ldt);
        System.out.println(jsonObject.getId()+taskInfo.getTiDescr());
        log.info(JSONObject.toJSONString(jsonObject));

        boolean defaultUrl = false;
        String url = taskInfo.getTiExternalCallBackUrl();
        String content = JSONObject.toJSONString(jsonObject);
        String datas = null;
        if(StringUtils.isNotEmpty(url)){
            //调用回调pushData：定时任务
            //String datas=OkHttpUtils.httpPostJson(taskInfo.getTiExternalCallBackUrl(), JSONObject.toJSONString(jsonObject));
            datas = tokenBucketUtils.throttlingCallBack(url, content);
        }else{
            defaultUrl = true;
            url = internalSystemAddress+"api/third/patrolAudit";
            datas=OkHttpUtils.httpPostJson(url, content);
        }

        if (StringUtils.isEmpty(datas) || (!datas.contains("\"code\":200") && !datas.contains("\"code\":500200") && !datas.contains("\"code\":500203"))) {
            //发送至mq
            PushDateDTO pushDateDTO = new PushDateDTO();
            pushDateDTO.setUrl(url);
            pushDateDTO.setContent(content);
            pushDateDTO.setDefaultUrl(defaultUrl);
            rabbitTemplate.convertAndSend(directFcb, keyFcb, JSONObject.toJSONString(pushDateDTO));
            log.warn("推送巡查数据失败：{}" , datas);
        }else {
            log.info("推送巡查数据成功：{}" , content);
        }
    }
}
