package com.bot.patrol.info.dispatch.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* <AUTHOR>
* @date 2022-03-03
*/
@ApiModel(value=" 定时任务管理修改",description=" 定时任务管理修改")
@Data
public class QuartzJobUpdateReq   {
        // ID
        @ApiModelProperty(value="ID",name="ID")
        private Long id;
        // Spring Bean名称
        @ApiModelProperty(value="Spring Bean名称",name="Spring Bean名称")
        private String beanName;
        // cron 表达式
        @ApiModelProperty(value="cron 表达式",name="cron 表达式")
        private String cronExpression;
        // 状态：1暂停、0启用
        @ApiModelProperty(value="状态：1暂停、0启用",name="状态：1暂停、0启用")
        private Boolean isUsing;
        // 任务名称
        @ApiModelProperty(value="任务名称",name="任务名称")
        private String jobName;
        // 方法名称
        @ApiModelProperty(value="方法名称",name="方法名称")
        private String methodName;
        // 参数
        @ApiModelProperty(value="参数",name="参数")
        private String params;
        // 备注
        @ApiModelProperty(value="备注",name="备注")
        private String remark;
        private String certType;
        private String taskType;


}
