package com.bot.patrol.info.dispatch.model.req;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
* <AUTHOR>
* @date 2022-02-08
*/
@ApiModel(value="订单列表查询对象",description="订单列表")
@Data
public class OrderListSearchReq {

        private String taskId;


}
