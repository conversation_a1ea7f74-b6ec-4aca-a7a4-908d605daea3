package com.bot.patrol.info.dispatch.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 *
 */
@ApiModel(value="微信回调数据",description="微信回调数据")
@Data
public class WeiBoCallBackReq {

    @ApiModelProperty(value="文章唯一标识",name="文章唯一标识")
    private String id;
    @ApiModelProperty(value="任务id(获取数据系统的id)",name="任务id(获取数据系统的id)")
    private String sid;
    @ApiModelProperty(value="文章内容(纯文本)",name="文章内容(纯文本)")
    private String contentByText;
    @ApiModelProperty(value="文章Url",name="文章Url")
    private String url;
    @ApiModelProperty(value="发布时间",name="发布时间")
    private LocalDateTime creatTime;
    @ApiModelProperty(value="转发数",name="转发数")
    private String retweetNums;
    @ApiModelProperty(value="点赞数", name="点赞数")
    private String likeNums;
    @ApiModelProperty(value="评论数", name="评论数")
    private String commentNums;
    @ApiModelProperty(value="文章内图片地址链接", name="文章内图片链接")
    private String imgUrls;
    @ApiModelProperty(value="文章内视频地址链接", name="文章内视频地址链接")
    private String videoUrls;
    @ApiModelProperty(value="截图地址链接", name="截图地址链接")
    private String snapshotUrl;


}
