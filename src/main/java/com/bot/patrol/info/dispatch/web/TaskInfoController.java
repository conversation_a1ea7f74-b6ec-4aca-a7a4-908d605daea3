package com.bot.patrol.info.dispatch.web;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bot.patrol.info.dispatch.entity.LookBackTask;
import com.bot.patrol.info.dispatch.entity.LookBackUrlList;
import com.bot.patrol.info.dispatch.entity.TaskInfo;
import com.bot.patrol.info.dispatch.entity.ext.LookBackItem;
import com.bot.patrol.info.dispatch.entity.ext.LookBackOrder;
import com.bot.patrol.info.dispatch.infrastructure.constants.CommonResult;
import com.bot.patrol.info.dispatch.infrastructure.constants.CommonStatusEnum;
import com.bot.patrol.info.dispatch.infrastructure.constants.EntityTypeEnum;
import com.bot.patrol.info.dispatch.infrastructure.constants.ErrorCodeEnum;
import com.bot.patrol.info.dispatch.infrastructure.utils.*;
import com.bot.patrol.info.dispatch.model.ESModel;
import com.bot.patrol.info.dispatch.model.ESModelDetail;
import com.bot.patrol.info.dispatch.model.IdUrlModel;
import com.bot.patrol.info.dispatch.model.Position;
import com.bot.patrol.info.dispatch.model.dto.PushDateDTO;
import com.bot.patrol.info.dispatch.model.req.*;
import com.bot.patrol.info.dispatch.schediule.ScheduledService;
import com.bot.patrol.info.dispatch.schediule.ScheduledWeiBoService;
import com.bot.patrol.info.dispatch.service.HttpApiService;
import com.bot.patrol.info.dispatch.service.ILookBackTaskService;
import com.bot.patrol.info.dispatch.service.ILookBackUrlListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
* <AUTHOR>
* @date 2022-02-08
*/
@Api(tags = "任务管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/task-info")
@Slf4j
public class TaskInfoController {

    private final HttpApiService httpApiService;
    //private final QuartzManage quartzManage;
    private final ILookBackTaskService lookBackTaskService;
    private final ILookBackUrlListService lookBackUrlListService;

    private final RedisUtils redisUtils;
    //private final IQuartzJobService quartzJobService;

    private final ScheduledWeiBoService scheduledWeiBoService;

    private final String LOOKBACK_MSG_PUSH_BOT_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=030bbf7d-386f-4db0-99f1-029c6bf612ea";

    @Autowired
    private RabbitTemplate rabbitTemplate;
    private String url="https://advancedapi.bazhuayu.com/";
    @Value("${patUrl}")
    private String patUrl;
    @Value("${qingboUrl}")
    private String qingboUrl;

    @Value("${weiboAddress}")
    private String weiboAddress;
    @Value("${qingboProjectId}")
    private String qingboProjectId;
    @Value("${qingboSign}")
    private String qingboSign;

    @Value("${days}")
    private Integer days;

    @Value("${internalSystemAddress}")
    private String internalSystemAddress;

    @Value("${convertPdfUrl}")
    private String convertPdfUrl;

    @Value("${customs.task.rabbitmq-direct-lookback}")
    private String rabbitmqLookBackDirect;
    @Value("${customs.task.rabbitmq-key-lookback-webo}")
    private String rabbitmqKeyLookBackWebo;
    @Value("${customs.task.rabbitmq-key-lookback-wechat}")
    private String rabbitmqKeyLookBackWechat;
    @Autowired
    private TokenBucketUtils tokenBucketUtils;
    @Value("${customs.props.customer-direct-fail-callback}")
    private String directFcb;
    @Value("${customs.props.customer-key-fail-callback}")
    private String keyFcb;

    @PostMapping(value = "/get-sno-by-url")
    public CommonResult getSnoByUrl(@RequestParam Map<String, Object> request){
        if (request.containsKey("url")){
            RunTaskCountByOlAiUrlReq req = new RunTaskCountByOlAiUrlReq();
            req.setOlAiUrl((String)request.get("url"));
            CommonResult  commonResult= httpApiService.getSnoByUrl( req);;
            if (ObjectUtils.isNotEmpty(commonResult.getData())){
                JSONArray ret = JSONArray.parseArray(JSON.toJSONString(commonResult.getData()));
                return CommonResult.success(ret);
            }else{
                return CommonResult.error(ErrorCodeEnum.CONTENT_NOT_EXIST);
            }
        }else{
            return CommonResult.error(ErrorCodeEnum.MISSING_PARAM_ERROR);
        }
    }

    @PostMapping(value = "/addByTaskAndOrder")
    @ApiOperation(value = "新增任务和订单", notes = "")
//    // @RequiresPermission("task.info.add")
    public CommonResult addByTaskAndOrder(@RequestBody @Valid TaskInfoAddReq request) throws Exception {
        if(request.getType()==null){
            return CommonResult.error(500, "type不能为空");
        }
        if(request.getTiTypeChild()!=null){
            if(request.getTiTypeChild().equals(1)){
                if(0==request.getType()){
                    return CommonResult.error(500, "type参数有误");
                }
            }
            if(request.getTiTypeChild().equals(2)){
                if(0!=request.getType()){
                    return CommonResult.error(500, "type参数有误");
                }
            }

        }else{
            return CommonResult.error(500, "tiTypeChild不能为空");
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        String taskType=null;
        if(0==request.getType()){
            request.setTiRunCycle(305101);
            taskType = EntityTypeEnum.TaskRunTypeEnum.INSTANT.getValue();
        }
        if(1==request.getType()){
            request.setTiRunCycle(305103);
            request.setTiRunType(306101);
            request.setTiCycleNum(cal.get(Calendar.DAY_OF_MONTH));
            taskType = EntityTypeEnum.CycleTypeEnum.Day.getValue();
        }
        if(2==request.getType()){
            request.setTiRunCycle(305103);
            request.setTiRunType(306102);
            request.setTiCycleNum(cal.get(Calendar.DAY_OF_WEEK));
            taskType = EntityTypeEnum.CycleTypeEnum.Week.getValue();
        }
        if(3==request.getType()){
            request.setTiRunCycle(305103);
            request.setTiRunType(306103);
            request.setTiCycleNum(cal.get(Calendar.MONTH));
            taskType = EntityTypeEnum.CycleTypeEnum.Month.getValue();
        }
        request.setTiStatus(EntityTypeEnum.TaskStatusTypeEnum.Stop.getCode());
        request.setTiStartTime( LocalDateTime.now());
        request.setTiState(1);
        CommonResult commonResult=httpApiService.addByTaskAndOrder( request);
        if(commonResult.getCode()==200) {
            if(ObjectUtils.isNotEmpty(commonResult.getData())) {
                if(request.getOrderLists().size()>0){
                    OrderList orderList=request.getOrderLists().get(0);
                    TaskInfo taskInfo = JSONObject.parseObject(JSON.toJSONString(commonResult.getData()), TaskInfo.class);
                    String botUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8f6f90b9-4c7e-46dd-99bd-962fd3800ec0";
                    String dingdingBotUrl = "https://oapi.dingtalk.com/robot/send?access_token=87ed27fd7095a893cb24a0470ff0ad71f91c5394ada701e38a880cec8051818e";
                    String tt="微博";
                    WeChatBotUtils weChatBot = new WeChatBotUtils(botUrl, false);
                    DingDingBotUtils dingChatBot = new DingDingBotUtils(dingdingBotUrl, false);
                    if(orderList.getOlAiSysType().equals(EntityTypeEnum.AppTypeEnum.WEB.getCode())){
                        tt="网页";
                    }else if(orderList.getOlAiSysType().equals(EntityTypeEnum.AppTypeEnum.WCAC.getCode())){
                        tt="公众号";
                    }
                    String bz="无";
                    if(StringUtils.isNotEmpty(request.getTiDescr())){
                        bz=request.getTiDescr();
                    }

                   if(orderList.getOlAiSysType().equals(EntityTypeEnum.AppTypeEnum.WEB.getCode())){
                       JSONObject jsonObject=new JSONObject();
                       jsonObject.put("taskSno", taskInfo.getTiSno());
                       jsonObject.put("url", request.getOrderLists().get(0).getOlAiUrl());
                       jsonObject.put("cycleType", request.getType());
                       // 发送文本消息
                       weChatBot.sendTextMsg(tt+ "巡查任务需求模板已生成（任务号[" + taskInfo.getTiSno() + "],地址[" + request.getOrderLists().get(0).getOlAiUrl() + "],任务类型["+taskType+"],备注["+bz+"]）\n"+
                               "请开始脚本编写，完成后根据需求启动云执行.\n"+
                               "需求示例-请查看定时采集设置，如未设置则写完脚本后执行一次，若其他则以下情况：\n"+
                               "1.点击日期后如选择得是按周执行，且选中了所有星期值，则每天执行\n"+
                               "2.点击日期后如选择得是按周执行，且选中了星期日，则每周执行一次\n"+
                               "3.点击日期后如选择得是按月执行，且选中了1号，则每月执行一次\n"+
                               "请根据配置情况按需调整配置日期，保证脚本完成后尽快执行交付！\n"+
                               "祝工作愉快！");
                       dingChatBot.sendTextMsg(tt+ "巡查任务需求模板已生成（任务号[" + taskInfo.getTiSno() + "],地址[" + request.getOrderLists().get(0).getOlAiUrl() + "],任务类型["+taskType+"],备注["+bz+"]）\n"+
                               "请开始脚本编写，完成后根据需求启动云执行.\n"+
                               "需求示例-请查看定时采集设置，如未设置则写完脚本后执行一次，若其他则以下情况：\n"+
                               "1.点击日期后如选择得是按周执行，且选中了所有星期值，则每天执行\n"+
                               "2.点击日期后如选择得是按周执行，且选中了星期日，则每周执行一次\n"+
                               "3.点击日期后如选择得是按月执行，且选中了1号，则每月执行一次\n"+
                               "请根据配置情况按需调整配置日期，保证脚本完成后尽快执行交付！\n"+
                               "祝工作愉快！");
//                       String ss=OkHttpUtils.httpPostJson(patUrl+"add-task-module", jsonObject.toJSONString());
//                       System.out.println(ss);
                   }else  if(orderList.getOlAiSysType().equals(EntityTypeEnum.AppTypeEnum.WCAC.getCode())){
                       if(!wechartId(request.getOrderLists().get(0).getOlAiUrl())||!wechartId(request.getOrderLists().get(0).getOlAiName())){
                           return CommonResult.error(500, "微信账号错误-"+request.getOrderLists().get(0).getOlAiUrl());
                       }
                       SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                       Calendar c = Calendar.getInstance();
                       c.add(Calendar.DAY_OF_MONTH, days);
                       Date finalDate = c.getTime();
                       String startDate=sdf.format(finalDate);
                       //调用清博接口，创建一个监测任务
                       String result=OkHttpUtils.postDataByForm(qingboUrl+"botsmart/botsmart/add-account","sid="+request.getOrderLists().get(0).getOlAiUrl()+"&account="+request.getOrderLists().get(0).getOlAiUrl()+"&project_id="+qingboProjectId+"&sign="+qingboSign+"&start_time="+startDate+" 00:00:00&end_time=2099-12-31 00:00:00",null);
                       log.info("调用清博接口，返回值：" + result);
                       if(StringUtils.isEmpty(result)){
                           TaskInfoUpdateReq taskInfoUpdateReq=new TaskInfoUpdateReq();
                           taskInfoUpdateReq.setSid(taskInfo.getSid());
                           taskInfoUpdateReq.setTiDelete(0);
                           httpApiService.update(taskInfoUpdateReq);
                           return CommonResult.error(500205, "清博公众号ID不存在["+orderList.getOlAiUrl()+"]");
                      }else{
                           JSONObject jsonObject=JSONObject.parseObject(result);
                           if(jsonObject.getInteger("code")==0){
                               // 发送文本消息
                               weChatBot.sendTextMsg(tt+ "巡查任务需求模板已生成（任务号[" + taskInfo.getTiSno() + "],地址[" + request.getOrderLists().get(0).getOlAiUrl() + "],任务类型["+taskType+"],备注["+bz+"]）\n"+
                                       "请开始脚本编写，完成后根据需求启动云执行.\n"+
                                       "需求示例-请查看定时采集设置，如未设置则写完脚本后执行一次，若其他则以下情况：\n"+
                                       "1.点击日期后如选择得是按周执行，且选中了所有星期值，则每天执行\n"+
                                       "2.点击日期后如选择得是按周执行，且选中了星期日，则每周执行一次\n"+
                                       "3.点击日期后如选择得是按月执行，且选中了1号，则每月执行一次\n"+
                                       "请根据配置情况按需调整配置日期，保证脚本完成后尽快执行交付！\n"+
                                       "祝工作愉快！");
                               dingChatBot.sendTextMsg(tt+ "巡查任务需求模板已生成（任务号[" + taskInfo.getTiSno() + "],地址[" + request.getOrderLists().get(0).getOlAiUrl() + "],任务类型["+taskType+"],备注["+bz+"]）\n"+
                                       "请开始脚本编写，完成后根据需求启动云执行.\n"+
                                       "需求示例-请查看定时采集设置，如未设置则写完脚本后执行一次，若其他则以下情况：\n"+
                                       "1.点击日期后如选择得是按周执行，且选中了所有星期值，则每天执行\n"+
                                       "2.点击日期后如选择得是按周执行，且选中了星期日，则每周执行一次\n"+
                                       "3.点击日期后如选择得是按月执行，且选中了1号，则每月执行一次\n"+
                                       "请根据配置情况按需调整配置日期，保证脚本完成后尽快执行交付！\n"+
                                       "祝工作愉快！");
                               return commonResult;
                           }else{
                               TaskInfoUpdateReq taskInfoUpdateReq=new TaskInfoUpdateReq();
                               taskInfoUpdateReq.setSid(taskInfo.getSid());
                               taskInfoUpdateReq.setTiDelete(0);
                               httpApiService.update(taskInfoUpdateReq);
                               return CommonResult.error(500, "添加失败");
                           }

                      }
//                       QuartzJob quartzJob = new QuartzJob();
//                       quartzJob.setJobName(taskInfo.getSid());
//                       quartzJob.setRemark(taskInfo.getTiDescr());
//                       quartzJob.setTaskType(taskInfo.getTiRunType());
//                       quartzJob.setCycleType(taskInfo.getTiRunCycle());
//                       quartzJob.setCycleNum(taskInfo.getTiCycleNum());
//                       quartzJob.setBeanName("scheduledService");
//                       quartzJob.setMethodName("run");
//                       ESModel jsonObject=new ESModel();
//                       jsonObject.setTaskId(taskInfo.getTiExternalId());
//                       jsonObject.setId(taskInfo.getSid());
//                       jsonObject.setCompanyId(taskInfo.getTiCpiSid());
//                       jsonObject.setTypeCode(302105);
//                       jsonObject.setProjectId(taskInfo.getTiPiSid());
//                       jsonObject.setAppId(null);
//                       jsonObject.setCaseId(null);
//                       jsonObject.setHitUrl(null);
//                       jsonObject.setCallBackUrl(request.getTiExternalCallBackUrl());
//                       jsonObject.setEventId(null);
//                       jsonObject.setSearchWordId(orderList.getOlAiSysType()+"");
//                       jsonObject.setModuleName(orderList.getOlAiUrl());
//                       quartzJob.setParams(JSONObject.toJSONString(jsonObject));
//                       quartzJob.setIsUsing(true);
//                       quartzJob.setStartTime(taskInfo.getTiStartTime());
//                       quartzJob.setEndTime(taskInfo.getTiEndTime());
//                       quartzJob.setCronExpression("0 0 12 * * ?");
//                       quartzJobService.insert(quartzJob);
//                       quartzManage.addJob(quartzJob);
                   }else{
                       if(0==request.getType()){
                           //请求新增微博账号接口
                           String str=OkHttpUtils.httpPostJson(weiboAddress+"getTask","{\"id\":\""+request.getOrderLists().get(0).getOlAiUrl()+"\"}");
                           log.info("请求微博数据："+str);
                           JSONObject jsonObject=JSONObject.parseObject(str);
                           JSONArray jsonArray=jsonObject.getJSONArray("msg");
                           if(ObjectUtils.isNotEmpty(jsonArray)||jsonArray.size()>0){
                               //抓取历史数据
                               LocalDateTime nows=LocalDateTime.now();
                               String endTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(nows);
                               LocalDateTime startTimes=nows.minusDays(101);
                               taskInfo.setTiCrawlerId(request.getOrderLists().get(0).getOlAiUrl());
                               scheduledWeiBoService.pushData(taskInfo,endTime,startTimes);
                               // 发送文本消息
                               weChatBot.sendTextMsg( "微博巡查任务需求模板已生成（任务号[" + taskInfo.getTiSno() + "],地址[" + request.getOrderLists().get(0).getOlAiUrl() + "],任务类型["+taskType+"],备注["+bz+"]）\n"+
                                       "没有历史数据请配置执行器");
                           }
//                           else{
//                               LocalDateTime nows=LocalDateTime.now();
//                               String endTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(nows);
//                               LocalDateTime startTimes=nows.minusDays(101);
//                               scheduledWeiBoService.pushData(taskInfo,endTime,startTimes);
//                           }

                       }else{
                           //请求新增微博账号接口
                           String str=OkHttpUtils.httpPostJson(weiboAddress+"addTask","{\"id\":\""+request.getOrderLists().get(0).getOlAiUrl()+"\"}");
                           log.info("请求微博数据："+str);
                           if(StringUtils.isNotEmpty(str)){
                               JSONObject jsonObject=JSONObject.parseObject(str);
                               if (jsonObject.getInteger("code")==200){
                                   LocalDateTime nows=LocalDateTime.now();
                                   String endTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(nows);
                                   LocalDateTime startTimes=nows.minusDays(101);
                                   taskInfo.setTiCrawlerId(request.getOrderLists().get(0).getOlAiUrl());
                                   scheduledWeiBoService.pushData(taskInfo,endTime,startTimes);
                                   return commonResult;
                               }else {
                                   TaskInfoUpdateReq taskInfoUpdateReq=new TaskInfoUpdateReq();
                                   taskInfoUpdateReq.setSid(taskInfo.getSid());
                                   taskInfoUpdateReq.setTiDelete(0);
                                   CommonResult commonResult1=httpApiService.update(taskInfoUpdateReq);
                                   return CommonResult.error(500, "添加失败");
                               }
                           }else{
                               TaskInfoUpdateReq taskInfoUpdateReq=new TaskInfoUpdateReq();
                               taskInfoUpdateReq.setSid(taskInfo.getSid());
                               taskInfoUpdateReq.setTiDelete(0);
                               CommonResult commonResult1=httpApiService.update(taskInfoUpdateReq);
                               return CommonResult.error(500, "添加失败");
                           }
                       }
                   }
                }


            }
        }
        return commonResult;
    }

    @PostMapping(value = "/addLookBackTask")
    @ApiOperation(value = "新增回查任务", notes = "")
    public CommonResult addLookBackTask(@RequestBody @Valid AddLookBackTaskReq request) throws Exception {
        try {
            List<LookBackOrder> lookBackOrders = request.getOrderLists();
            LookBackTask lookBackTask = new LookBackTask();
            lookBackTask.setLbtSno("XHLB-" + CommUtils.getSno());
            lookBackTask.setLbtType(request.getTiType());
            lookBackTask.setLbtAppType(lookBackOrders.get(0).getOlAiSysType());
            lookBackTask.setLbtState(0);
            lookBackTask.setLbtOrigin(EntityTypeEnum.OriginEnum.EXT_USER.getCode());
            lookBackTask.setLbtExternalId(request.getTiExternalId());
            lookBackTask.setLbtCallbackUrl(request.getTiExternalCallBackUrl());
            //新增回查任务
            lookBackTaskService.add(lookBackTask);
            List<LookBackItem> lookBackItems = request.getDocList();
            WeChatBotUtils weChatBot = new WeChatBotUtils(LOOKBACK_MSG_PUSH_BOT_URL, false);
            StringBuilder fileText = new StringBuilder();
            for (LookBackItem lookBackItem : lookBackItems) {
                LookBackUrlList lookBackUrlList = new LookBackUrlList();
                lookBackUrlList.setLulLbtSid(lookBackTask.getSid());
                lookBackUrlList.setLulExternalId(lookBackItem.getDocId());
                lookBackUrlList.setLulUrl(lookBackItem.getUrl());
                lookBackUrlList.setLulState(0);
                lookBackUrlListService.add(lookBackUrlList);
                JSONObject jo = new JSONObject();
                jo.put("taskSid", lookBackTask.getSid());
                jo.put("itemSid", lookBackUrlList.getSid());
                jo.put("url", lookBackUrlList.getLulUrl());
                fileText.append(lookBackUrlList.getLulUrl()).append("\n");
                if (EntityTypeEnum.AppTypeEnum.WEBO.getCode() == lookBackOrders.get(0).getOlAiSysType()) {
                    rabbitTemplate.convertAndSend(rabbitmqLookBackDirect, rabbitmqKeyLookBackWebo, JSONObject.toJSONString(jo));
                } else if (EntityTypeEnum.AppTypeEnum.WCAC.getCode() == lookBackOrders.get(0).getOlAiSysType()) {
                    rabbitTemplate.convertAndSend(rabbitmqLookBackDirect, rabbitmqKeyLookBackWechat, JSONObject.toJSONString(jo));
                }
            }
            if (EntityTypeEnum.AppTypeEnum.WEB.getCode() == lookBackOrders.get(0).getOlAiSysType()){
                weChatBot.sendTextMsg("回查任务号[" + lookBackTask.getLbtSno() + "]\n"
                        + "任务类型[即时执行]\n"
                        + "备注[网页]\n"
                        + "注意：任务须在回查任务组进行编写，完成后即时执行。\n"
                        + "地址列表见附件,祝工作愉快！");
            } else if (EntityTypeEnum.AppTypeEnum.WEBO.getCode() == lookBackOrders.get(0).getOlAiSysType()) {
                weChatBot.sendTextMsg("回查任务已生成（任务号[" + lookBackTask.getLbtSno() + "],备注[微博]），此为任务提示信息！无需编写脚本！祝工作愉快！");
            } else if (EntityTypeEnum.AppTypeEnum.WCAC.getCode() == lookBackOrders.get(0).getOlAiSysType()) {
                weChatBot.sendTextMsg("回查任务已生成（任务号[" + lookBackTask.getLbtSno() + "],备注[微信]），此为任务提示信息！无需编写脚本！祝工作愉快！");
            }
            File tempFile = File.createTempFile("附件：" + lookBackTask.getLbtSno(), ".txt");
            FileOutputStream fos = new FileOutputStream(tempFile);
            fos.write(fileText.toString().getBytes());
            fos.close();
            String fileName = lookBackTask.getLbtSno() + ".txt";
            if (EntityTypeEnum.AppTypeEnum.WEBO.getCode() == lookBackOrders.get(0).getOlAiSysType() || EntityTypeEnum.AppTypeEnum.WCAC.getCode() == lookBackOrders.get(0).getOlAiSysType()){
                fileName = "可忽略附件：" + lookBackTask.getLbtSno() + ".txt";
            }else{
                fileName = "附件：" + lookBackTask.getLbtSno() + ".txt";
            }
            weChatBot.sendFileMsg(tempFile, fileName);
            return CommonResult.success("任务添加成功");
        } catch (Exception ex){
            return CommonResult.error(500, ex.getMessage());
        }

    }
    public  boolean wechartId(String id){
//        try{
//            Document document = Jsoup.connect("https://wx.sogou.com/weixin?type=1&query="+id+"&ie=utf8&s_from=input&_sug_=y&_sug_type_=").get();
//            Elements elements=document.getElementsByClass("news-list2");
//            if(elements.size()>0){
//                if(elements.get(0).getElementsByAttributeValue("name","em_weixinhao").get(0).text().equals(id)){
//                    return true;
//                }
//            }else{
//                Document documents = Jsoup.connect("https://weixin.sogou.com/weixin?type=2&query="+id+"&ie=utf8&s_from=input&_sug_=y&_sug_type_=").get();
//                Elements elementes=documents.getElementsByClass("news-list");
//                if(elementes.size()>0){
//                    return true;
//                }
//            }
//        }catch (Exception e){
//            log.error("请求搜狗异常");
//            return true;
//        }
        return true;
    }
    @PostMapping(value = "/updateByExternalId")
    @ApiOperation(value = "更新任务状态", notes = "")
//    // @RequiresPermission("task.info.add")
    public CommonResult updateByExternalId(@RequestBody @Valid TaskInfoUpdateReq request) throws IOException {
        if(StringUtils.isEmpty(request.getTiExternalId())){
            return CommonResult.error(500,"id必传");
        }
        if(ObjectUtils.isEmpty(request.getTiType())){
            return CommonResult.error(500,"任务类型必传");
        }
        if(request.getTiStatus().equals(1)){
            request.setTiStatus(EntityTypeEnum.TaskStatusTypeEnum.Start.getCode());
            request.setTiDelete(CommonStatusEnum.EFFECTIVE.getCode());
            request.setTiState(CommonStatusEnum.EFFECTIVE.getCode());
        }else {
            request.setTiStatus(EntityTypeEnum.TaskStatusTypeEnum.Stop.getCode());
            request.setTiDelete(CommonStatusEnum.NO_EFFECTIVE.getCode());
            request.setTiState(CommonStatusEnum.NO_EFFECTIVE.getCode());
        }
        GetBZYToken getBZYToken=new GetBZYToken(redisUtils);
        CommonResult commonResult=httpApiService.updateByExternalId( request);
        log.info("更新数据"+commonResult);
        if(commonResult.getCode()==200) {
            if(ObjectUtils.isNotEmpty(commonResult.getData())){
                TaskInfo taskInfo= JSONObject.parseObject(JSON.toJSONString(commonResult.getData()), TaskInfo.class);
                OrderListSearchReq orderListSearchReq=new OrderListSearchReq();
                orderListSearchReq.setTaskId(taskInfo.getSid());
                CommonResult commonResult1=httpApiService.getOneByList(orderListSearchReq);
                log.info("查询数据"+commonResult1);
                if(commonResult1.getCode()==200) {
                    if(ObjectUtils.isNotEmpty(commonResult1.getData())){
                        OrderList orderList= JSONObject.parseObject(JSON.toJSONString(commonResult1.getData()), OrderList.class);
                        if(orderList.getOlAiSysType().equals(300300)){
                            if(taskInfo.getTiStatus()==EntityTypeEnum.TaskStatusTypeEnum.Stop.getCode()){
                                JSONObject jsonObject=new JSONObject();
                                jsonObject.put("taskSno", taskInfo.getTiSno());
//                    String s1= OkHttpUtils.httpPostJson(url+"api/task/StopTask?taskId="+taskInfo.getTiCrawlerId(), getBZYToken.getToken(), "{}");
//                                String ss=OkHttpUtils.httpPostJson(patUrl+"stop-task", jsonObject.toJSONString());
//                                System.out.println(ss);
                                return CommonResult.success();
                            }else if(taskInfo.getTiStatus()==EntityTypeEnum.TaskStatusTypeEnum.Start.getCode()){
//                                String s1= OkHttpUtils.httpPostJson(url+"api/task/StartTask?taskId="+taskInfo.getTiCrawlerId(), getBZYToken.getToken(), "{}");
//                                return CommonResult.error(200,s1);
                                return CommonResult.success();
                            }
                        }else  if(orderList.getOlAiSysType().equals(300303)){
                            TaskInfoSearchReq taskInfoSearchReq = new TaskInfoSearchReq();
                            taskInfoSearchReq.setAppUrl(orderList.getOlAiUrl());
                            taskInfoSearchReq.setAppType(EntityTypeEnum.AppTypeEnum.WCAC.getCode());
                            CommonResult commonResults = httpApiService.listTaskByOrderUrl(taskInfoSearchReq);
                            log.info("公众号查询"+commonResults);
                            List<TaskInfo> list = new ArrayList<>();
                            if (200 == commonResults.getCode()) {
                                if (ObjectUtils.isNotEmpty(commonResults.getData())) {
                                    list = (List<TaskInfo>) commonResults.getData();
                                }
                            }
                            log.info("公众号查询"+list.size());
//                            if(list.size()>0){
//                                return commonResult;
//                            }
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            Calendar c = Calendar.getInstance();
                            c.add(Calendar.DAY_OF_MONTH, -100);
                            Date finalDate = c.getTime();
                            Calendar c1 = Calendar.getInstance();
                            c1.add(Calendar.DAY_OF_MONTH, -1);
                            Date endDate = c1.getTime();
                            String startDate=sdf.format(finalDate);
                            String endDates=sdf.format(endDate);
//                            if (taskInfo.getTiStatus()==EntityTypeEnum.TaskStatusTypeEnum.Stop.getCode()){
//                                RunTaskCountByOlAiUrlReq req = new RunTaskCountByOlAiUrlReq();
//                                req.setOlAiUrl(orderList.getOlAiUrl());
//                                CommonResult commonResult2 = httpApiService.getRunTaskCountByOlAiUrl(req);
//                                if (200 == commonResult2.getCode()) {
//                                    if (ObjectUtils.isNotEmpty(commonResult2.getData())) {
//                                        if((Integer)commonResult2.getData() > 0){
//                                            endDates="2099-12-31 00:00:00";
//                                        }
//                                    }
//                                } else {
//                                    return CommonResult.error(500, "更新失败1");
//                                }
//                            }

                            if(taskInfo.getTiStatus()==EntityTypeEnum.TaskStatusTypeEnum.Start.getCode()){
                                endDates="2099-12-31 00:00:00";
                            }
                            String ss=OkHttpUtils.postDataByForm(qingboUrl+"botsmart/botsmart/add-account","sid="+orderList.getOlAiUrl()+"&account="+orderList.getOlAiUrl()+"&project_id="+qingboProjectId+"&sign="+qingboSign+"&start_time="+startDate+" 00:00:00&end_time="+endDates+" 00:00:00",null);
                            System.out.println(ss);
                            log.info("更新清博"+ss);
                            if(StringUtils.isEmpty(ss)){
                                return CommonResult.error(500205, "清博公众号ID不存在["+orderList.getOlAiUrl()+"]");
                            }else{
                                JSONObject jsonObject=JSONObject.parseObject(ss);
                                if(jsonObject.getInteger("code")==0){
                                    return commonResult;
                                }else{
                                    return CommonResult.error(500, "更新失败2");
                                }

                            }
//                            QuartzJob quartzJob=quartzJobService.selectByJobName(taskInfo.getSid());
//                            if(request.getTiStatus()==2){
//                                if(quartzJob.getIsUsing()){
//                                    quartzJobService.pauseAndResume(quartzJob.getId(), false);
//                                    return CommonResult.error(200,"更新成功");
//                                }
//                            }else if(request.getTiStatus()==1){
//                                if(!quartzJob.getIsUsing()){
//                                    quartzJobService.pauseAndResume(quartzJob.getId(), true);
//                                    return CommonResult.error(200,"更新成功");
//                                }
//                            }
                        }else {
                            return CommonResult.success();
                        }
                    }
                }
            }
        }else{
            return commonResult;
        }
        return CommonResult.error(500,"更新失败");
    }
    private List<String> listImg=new ArrayList<String>(){
        {
            this.add(".gif");
            this.add(".jpeg");
            this.add(".png");
            this.add(".jpg");
            this.add(".bmp");
            this.add(".GIF");
            this.add(".JPEG");
            this.add(".PNG");
            this.add(".JPG");
            this.add(".BMP");
        }
    };
    @PostMapping(value = "/pushData")
    @ApiOperation(value = "手动推送数据", notes = "")
    public CommonResult pushData(@RequestBody @Valid PushDataReq request)  {
        TaskInfoSearchReq taskInfoSearchReq=new TaskInfoSearchReq();
        taskInfoSearchReq.setTiSno(request.getTaskNo());
        CommonResult commonResult=httpApiService.list(taskInfoSearchReq);
        List<TaskInfo> list=new ArrayList<>();
        if(200==commonResult.getCode()){
            if(ObjectUtils.isNotEmpty(commonResult.getData())){
                list= (List<TaskInfo>) commonResult.getData();
            }
        }
        TaskInfo taskInfo=null;
        if(list.size()>0){
             taskInfo=JSONObject.parseObject(JSONObject.toJSONString(list.get(0)),TaskInfo.class);
        }else {
            return  CommonResult.error(500,"未查询到任务数据");
        }
        for (int i = 0; i < request.getJsonArray().size() ; i++) {
            JSONObject jsonObject2=request.getJsonArray().getJSONObject(i);
            log.info("从接口推送过来的数据"+jsonObject2);
            String title=jsonObject2.getString("标题");
            String titleUrl=jsonObject2.getString("标题链接");
            String time=jsonObject2.getString("时间");
            String html=jsonObject2.containsKey("html") ? jsonObject2.getString("html") : null;
            jsonObject2.remove("标题");
            jsonObject2.remove("标题链接");
            jsonObject2.remove("时间");
            if(Objects.nonNull(html)){
                jsonObject2.remove("html");
            }
            Map<String, String> jsonMap = JSONObject.toJavaObject(jsonObject2, Map.class);
            ESModel jsonObject=new ESModel();
//                        jsonObject.setCompanyId(taskInfo.getTiCpiSid());
            if(jsonObject2.containsKey("upNum")){
                jsonObject.setThumbUpFor(jsonObject2.getString("upNum"));
                jsonObject2.remove("upNum");
            }
            if(jsonObject2.containsKey("retweetNum")){
                jsonObject.setForwardingNumber(jsonObject2.getString("retweetNum"));
                jsonObject2.remove("retweetNum");
            }
            if(jsonObject2.containsKey("commentNum")){
                jsonObject.setComments(jsonObject2.getString("commentNum"));
                jsonObject2.remove("commentNum");
            }
            if(jsonObject2.containsKey("webPdfSnapshot")){
                jsonObject.setWebPdfSnapshot(jsonObject2.getString("webPdfSnapshot"));
                jsonObject2.remove("webPdfSnapshot");
            }
            jsonObject.setId(taskInfo.getSid());
            jsonObject.setProjectId(null);
            jsonObject.setDataId(UUID.randomUUID().toString().trim().replaceAll("-", ""));
            jsonObject.setTitle(title);
            jsonObject.setAppId(null);
            jsonObject.setTaskId(taskInfo.getTiExternalId());
            jsonObject.setCaseId(null);
            jsonObject.setHitUrl(titleUrl);
            if(StringUtils.isNotEmpty(time)){
                jsonObject.setScreenTime(ScheduledService.ifTime(time));
            }else {
                DateTimeFormatter dfg = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String string = new SimpleDateFormat("yyyy-MM-dd").format(new Date()).toString();
                String time1 = new SimpleDateFormat("HH:mm:ss").format(new Date()).toString();
                jsonObject.setScreenTime(LocalDateTime.parse(string+" "+time1,dfg));
            }
            jsonObject.setEventId(null);
            jsonObject.setSearchWordId(null);
            jsonObject.setModuleName(request.getModelName());
            jsonObject.setTypeCode(taskInfo.getTiType());
            jsonObject.setReadTheNumber("0");
            jsonObject.setThumbUpFor("0");
            jsonObject.setLookingAtTheNumber("0");
            jsonObject.setForwardingNumber("0");
            jsonObject.setComments("0");
            List<ESModelDetail> esModelDetails=new ArrayList<>();
            List<String> im=new ArrayList<>();
            StringBuffer stringBuffer = new StringBuffer();
            for (String key : jsonMap.keySet()) {
                String value = jsonMap.get(key);
                if(ScheduledService.isHttpUrl(value)){
                    for (int l = 0; l < listImg.size(); l++) {
                        if(value.indexOf(listImg.get(l))>=0){
                            im.add(value);
                        }
//                                    else{
//                                        jsonObject.setHitUrl(value);
//                                    }
                    }

                }else{
                    ESModelDetail esModelDetail=new ESModelDetail();
                    esModelDetail.setWord(value);
                    Position position=new Position();
                    esModelDetail.setPosition(position);
                    esModelDetails.add(esModelDetail);
                    if(StringUtils.isNotEmpty(value)){
                        value=ScheduledService.replaceBlank(value);
                    }
                    stringBuffer.append(value);
                }
            }
            jsonObject.setText(stringBuffer.toString());
            jsonObject.setEsModelDetailList(esModelDetails);
//            List<String> attachmentList=new ArrayList<>();
//            List<String> veidoList=new ArrayList<>();
//            if (html != null) {
//                Document document = Jsoup.parse(html);
//                List<String> imgs = document.select("img").eachAttr("src");
//                if(!imgs.isEmpty()){
//                    for(String imgUrl: imgs) {
//                        im.add(CommUtils.parseSrc(imgUrl, titleUrl));
//                    }
//                }
//                List<String> attachments = document.select("a").eachAttr("href");
//                if (!attachments.isEmpty()) {
//                    for(String attachmentUrl: attachments) {
//                        attachmentList.add(CommUtils.parseSrc(attachmentUrl, titleUrl));
//                    }
//                }
//                List<String> videos = document.select("video").eachAttr("src");
//                if (!videos.isEmpty()) {
//                    for(String videoUrl: videos) {
//                        veidoList.add(CommUtils.parseSrc(videoUrl, titleUrl));
//                    }
//                }
//            }
            jsonObject.setImgList(im);
//            jsonObject.setAttachmentList(attachmentList);
//            jsonObject.setVideoList(veidoList);
            jsonObject.setCallBackUrl(null);
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime ldt = LocalDateTime.parse(jsonObject.getScreenTime().format(df),df);
            jsonObject.setScreenTime(ldt);
            System.out.println(jsonObject.getId()+taskInfo.getTiDescr());
            log.info(JSONObject.toJSONString(jsonObject));
            List<IdUrlModel> idUrlModels=new ArrayList<>();
            IdUrlModel idUrlModel=new IdUrlModel();
            idUrlModel.setId(jsonObject.getDataId());
            idUrlModel.setUrl(jsonObject.getHitUrl());
            idUrlModels.add(idUrlModel);
//            String postJson=OkHttpUtils.httpPostJson(convertPdfUrl,JSONObject.toJSONString(idUrlModels));

            String url = taskInfo.getTiExternalCallBackUrl();
            String content = JSONObject.toJSONString(jsonObject);
            String datas = null;
            boolean defaultUrl = false;
            if(StringUtils.isNotEmpty(url)){
                //String datas=OkHttpUtils.httpPostJson(taskInfo.getTiExternalCallBackUrl(), JSONObject.toJSONString(jsonObject));
                //调用回调pushData：手动
                datas = tokenBucketUtils.throttlingCallBack(url, content);
            }else{
                defaultUrl = true;
                url = internalSystemAddress + "api/third/patrolAudit";
                datas = OkHttpUtils.httpPostJson(url, content);
            }

            if (StringUtils.isEmpty(datas) || (!datas.contains("\"code\":200") && !datas.contains("\"code\":500200") && !datas.contains("\"code\":500203"))) {
                //发送至mq
                PushDateDTO pushDateDTO = new PushDateDTO();
                pushDateDTO.setUrl(url);
                pushDateDTO.setContent(content);
                pushDateDTO.setDefaultUrl(defaultUrl);
                rabbitTemplate.convertAndSend(directFcb, keyFcb, JSONObject.toJSONString(pushDateDTO));
                log.warn("推送巡查数据失败(手动)：{}" , datas);
            } else {
                log.info("推送巡查数据成功(手动)：{}" , content);
            }
        }
        return CommonResult.success(taskInfo);
    }

    @PostMapping(value = "/pushCallbackData")
    @ApiOperation(value = "手动推送数据", notes = "")
    public CommonResult pushCallbackData(@RequestBody @Valid PushCallbackDataReq request)  {
        LookBackTask task = lookBackTaskService.selectByTaskNo(request.getTaskNo());
        JSONArray pushErrorDatas = new JSONArray();
        if(Objects.isNull(task)){
            return  CommonResult.error(500,"未查询到任务数据");
        }
        for (int i = 0; i < request.getJsonArray().size() ; i++) {
            JSONObject pushErrorData = new JSONObject();
            JSONObject jsonObject2=request.getJsonArray().getJSONObject(i);
            log.info("从接口推送过来的数据"+jsonObject2);
            String title=jsonObject2.getString("标题");
            String titleUrl=jsonObject2.getString("标题链接");
            String time=jsonObject2.getString("时间");
            String html=jsonObject2.containsKey("html") ? jsonObject2.getString("html") : null;
            jsonObject2.remove("标题");
            jsonObject2.remove("标题链接");
            jsonObject2.remove("时间");
            if(Objects.nonNull(html)){
                jsonObject2.remove("html");
            }
            Map<String, String> jsonMap = JSONObject.toJavaObject(jsonObject2, Map.class);
            ESModel jsonObject=new ESModel();
//                        jsonObject.setCompanyId(taskInfo.getTiCpiSid());
            if(jsonObject2.containsKey("upNum")){
                jsonObject.setThumbUpFor(jsonObject2.getString("upNum"));
                jsonObject2.remove("upNum");
            }
            if(jsonObject2.containsKey("retweetNum")){
                jsonObject.setForwardingNumber(jsonObject2.getString("retweetNum"));
                jsonObject2.remove("retweetNum");
            }
            if(jsonObject2.containsKey("commentNum")){
                jsonObject.setComments(jsonObject2.getString("commentNum"));
                jsonObject2.remove("commentNum");
            }
            if(jsonObject2.containsKey("webPdfSnapshot")){
                jsonObject.setWebPdfSnapshot(jsonObject2.getString("webPdfSnapshot"));
                jsonObject2.remove("webPdfSnapshot");
            }
            List<LookBackUrlList> lookBackUrlList = lookBackUrlListService.selectByTaskSidAndUrl(task.getSid(), titleUrl);
            if (Objects.isNull(lookBackUrlList) || lookBackUrlList.isEmpty()){
                continue;
            }
            jsonObject.setId(task.getSid());
            jsonObject.setProjectId(null);
            jsonObject.setDataId(UUID.randomUUID().toString().trim().replaceAll("-", ""));
            jsonObject.setTitle(title);
            jsonObject.setAppId(null);
            jsonObject.setTaskId(task.getLbtExternalId());
            jsonObject.setCaseId(null);
            jsonObject.setHitUrl(titleUrl);
            if(StringUtils.isNotEmpty(time)){
                jsonObject.setScreenTime(ScheduledService.ifTime(time));
            }else {
                DateTimeFormatter dfg = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String string = new SimpleDateFormat("yyyy-MM-dd").format(new Date()).toString();
                String time1 = new SimpleDateFormat("HH:mm:ss").format(new Date()).toString();
                jsonObject.setScreenTime(LocalDateTime.parse(string+" "+time1,dfg));
            }
            jsonObject.setEventId(null);
            jsonObject.setSearchWordId(null);
            jsonObject.setModuleName(request.getModelName());
            jsonObject.setTypeCode(task.getLbtType());
            jsonObject.setReadTheNumber("0");
            jsonObject.setThumbUpFor("0");
            jsonObject.setLookingAtTheNumber("0");
            jsonObject.setForwardingNumber("0");
            jsonObject.setComments("0");
            List<ESModelDetail> esModelDetails=new ArrayList<>();
            List<String> im=new ArrayList<>();
            StringBuffer stringBuffer = new StringBuffer();
            for (String key : jsonMap.keySet()) {
                String value = jsonMap.get(key);
                if(ScheduledService.isHttpUrl(value)){
                    for (int l = 0; l < listImg.size(); l++) {
                        if(value.indexOf(listImg.get(l))>=0){
                            im.add(value);
                        }
//                                    else{
//                                        jsonObject.setHitUrl(value);
//                                    }
                    }

                }else{
                    ESModelDetail esModelDetail=new ESModelDetail();
                    esModelDetail.setWord(value);
                    Position position=new Position();
                    esModelDetail.setPosition(position);
                    esModelDetails.add(esModelDetail);
                    if(StringUtils.isNotEmpty(value)){
                        value=ScheduledService.replaceBlank(value);
                    }
                    stringBuffer.append(value);
                }
            }
            jsonObject.setText(stringBuffer.toString());
            jsonObject.setEsModelDetailList(esModelDetails);
//            List<String> attachmentList=new ArrayList<>();
//            List<String> veidoList=new ArrayList<>();
//            if (html != null) {
//                Document document = Jsoup.parse(html);
//                List<String> imgs = document.select("img").eachAttr("src");
//                if(!imgs.isEmpty()){
//                    for(String imgUrl: imgs) {
//                        im.add(CommUtils.parseSrc(imgUrl, titleUrl));
//                    }
//                }
//                List<String> attachments = document.select("a").eachAttr("href");
//                if (!attachments.isEmpty()) {
//                    for(String attachmentUrl: attachments) {
//                        attachmentList.add(CommUtils.parseSrc(attachmentUrl, titleUrl));
//                    }
//                }
//                List<String> videos = document.select("video").eachAttr("src");
//                if (!videos.isEmpty()) {
//                    for(String videoUrl: videos) {
//                        veidoList.add(CommUtils.parseSrc(videoUrl, titleUrl));
//                    }
//                }
//            }
            jsonObject.setImgList(im);
//            jsonObject.setAttachmentList(attachmentList);
//            jsonObject.setVideoList(veidoList);
            jsonObject.setCallBackUrl(null);
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime ldt = LocalDateTime.parse(jsonObject.getScreenTime().format(df),df);
            jsonObject.setScreenTime(ldt);
            for(LookBackUrlList lul: lookBackUrlList){
                jsonObject.setDataId(lul.getLulExternalId());
                System.out.println(jsonObject.getId()+task.getLbtDescr());
                log.info(JSONObject.toJSONString(jsonObject));
                List<IdUrlModel> idUrlModels=new ArrayList<>();
                IdUrlModel idUrlModel=new IdUrlModel();
                idUrlModel.setId(jsonObject.getDataId());
                idUrlModel.setUrl(jsonObject.getHitUrl());
                idUrlModels.add(idUrlModel);
//                String postJson=OkHttpUtils.httpPostJson(convertPdfUrl,JSONObject.toJSONString(idUrlModels));
//                System.out.println("请求html转换返回数据:"+postJson);

                String url = task.getLbtCallbackUrl();
                String content = JSONObject.toJSONString(jsonObject);
                String datas = null;
                boolean defaultUrl = false;
                if(StringUtils.isNotEmpty(url)){
                    //调用回调pushData：手动
                    //String datas=OkHttpUtils.httpPostJson(task.getLbtCallbackUrl(), JSONObject.toJSONString(jsonObject));
                    datas = tokenBucketUtils.throttlingCallBack(url, content);
                }else{
                    defaultUrl = true;
                    url = internalSystemAddress + "api/third/patrolAudit";
                    datas=OkHttpUtils.httpPostJson(url, content);
                }
                if (StringUtils.isEmpty(datas) || (!datas.contains("\"code\":200") && !datas.contains("\"code\":500200") && !datas.contains("\"code\":500203"))) {
                    pushErrorData.put("titleUrl", titleUrl);
                    pushErrorData.put("dataExternalId", lul.getLulExternalId());
                    pushErrorData.put("dataNum", lookBackUrlList.size());
                    pushErrorData.put("errorMsg", datas);
                    pushErrorDatas.add(pushErrorData);

                    PushDateDTO pushDateDTO = new PushDateDTO();
                    pushDateDTO.setUrl(url);
                    pushDateDTO.setContent(content);
                    pushDateDTO.setDefaultUrl(defaultUrl);
                    lul.setLulState(1);
                    pushDateDTO.setLookBackUrlList(lul);
                    if(lookBackUrlListService.getFinishStateByTaskSid(task.getSid())){
                        task.setLbtState(1);
                        pushDateDTO.setLookBackTask(task);
                    }
                    rabbitTemplate.convertAndSend(directFcb, keyFcb, JSONObject.toJSONString(pushDateDTO));
                    log.warn("推送回查数据失败(手动)：{}" , datas);
                }else {
                    lul.setLulState(1);
                    lookBackUrlListService.updateEntity(lul);
                    if (lookBackUrlListService.getFinishStateByTaskSid(task.getSid())) {
                        task.setLbtState(1);
                        lookBackTaskService.updateEntity(task);
                    }
                    log.info("推送回查数据成功(手动)：{}" , content);
                }
            }
        }
        if (!pushErrorDatas.isEmpty()){
            return CommonResult.success(pushErrorDatas);
        }else {
            return CommonResult.success(task);
        }
    }

    //@PostMapping(value = "/testThrottling")
    public String testThrottling() {
        //System.out.println("测试！");
        //String url = "http://www.baidu.com";
        /*String result = tokenBucketUtils.throttlingCallBack(url, "xxxx");
        if (StringUtils.isNotEmpty(result)) {
            System.out.println("result1 :" + result);
        }else {
            System.out.println("空1");
        }
        url = "http://www.hhh.com";
        result = tokenBucketUtils.throttlingCallBack(url, "xxxx");
        if (StringUtils.isNotEmpty(result)) {
            System.out.println("result2 :" + result);
        }else {
            System.out.println("空2");
        }*/
        String url = "http://10.0.0.77:9077/notify/add";
        String content = "{\"comments\":\"0\",\"dataId\":\"ea7ab66655644bf49285067e3fe76492\",\"esModelDetailList\":[{\"position\":{},\"word\":\"尊敬的广大投资者：\\n\\n\\n我司近期发现有不法分子假冒国信证券名义进行非法证券活动。主要方式为：不法分子通过抖音等网络平台或其他方式联系投资者，向投资者发送假冒我司的网站链接、软件APP，或者冒充我司员工，引诱投资者转账或者充值，随后资金无法取出。\\n\\n假冒下载链接或网页为：\\n\\n1.6828119.com/index/login\\n\\n 2.43.136.30.106:99\\n\\n3.https://kmapp9.xyz/#/login\\n\\n4.www.akhyiva.c\\n\\n;5.WXL3cJrAX83GsSYe8CP6bD9xGKpr36cGL\\n\\n不法分子使用的抖音号为：28604118735\\n\\n请广大投资者注意识别相关非法人员、网站和各类手机APP或软件，谨防诈骗，保护好个人信息，通过正规渠道进行投资理财，避免高收益诱惑而造成资金损失。\\n\\n我司对此郑重声明：\\n\\n（一）我司和我司员工未参与上述非法证券活动，上述活动系不法分子冒充我司和我司员工名义进行，与我司及我司员工无关。\\n\\n（二）我司炒股软件为手机金太阳APP、金太阳网上交易软件，请广大投资者通过我司官网等正规渠道下载，切勿通过不明网址、二维码进行交易或者下载软件。\\n\\n（三）我司从未以个人名义开展投资咨询业务或服务。任何以个人名义开展投资咨询、收取会员费、承诺收益、盈利分成等，均属非法投资咨询活动。\\n\\n（四）我司开展相关业务从未要求投资者将有关款项支付到任何个人或者对公账户。任何以我司工作人员个人身份开展业务并要求投资者支付到个人或者对公账户的行为，均属违法。\\n\\n（五）我司全国统一服务电话为95536，公司网站为www.guosen.com.cn，微信号为gx002736，官方微博为https://weibo.com/u/5053736747，投资者可以通过上述渠道或者亲临我司当地营业部对工作人员的身份进行核实和提出业务咨询。\\n\\n（六）对任何冒充我司或我司工作人员的非法行为，我司将采取一切必要之法律手段以追究其责任。\\n\\n敬请投资者明辨真伪，警惕此类非法证券活动，切实保护自身利益，谨防受骗，同时欢迎广大投资者积极向我司举报有关非法活动线索。\\n\\n特此公告！\\n\\n \\n\\n                               国信证券股份有限公司\\n\\n                                    2023年6月26日\\n\\n附假冒链接或网页截图：\\n\\n \\n\\n\\n\\n\\n\\n\\n\\n【免责声明】以上信息仅用于投资者教育，不构成任何投资建议，投资者不应以该等信息取代其独立判断或仅根据该等信息做出决策。国信证券力求该宣传内容的准确可靠，但对其准确性或完整性不作保证，亦不对使用该信息而引发或可能引发的损失承担任何责任。\\n\\n\\n\"}],\"forwardingNumber\":\"0\",\"hitUrl\":\"https://www.guosen.com.cn/gs/edu/classroom_detail.html?id=62902\",\"id\":\"e072315045125ef8be031767ed6500cf\",\"imgList\":[],\"lookingAtTheNumber\":\"0\",\"moduleName\":\"XH-168776942323748-3_复制\",\"readTheNumber\":\"0\",\"screenTime\":\"2023-09-26 10:06:47\",\"taskId\":\"81\",\"text\":\"尊敬的广大投资者：我司近期发现有不法分子假冒国信证券名义进行非法证券活动。主要方式为：不法分子通过抖音等网络平台或其他方式联系投资者，向投资者发送假冒我司的网站链接、软件APP，或者冒充我司员工，引诱投资者转账或者充值，随后资金无法取出。假冒下载链接或网页为：1.6828119.com/index/login 2.43.136.30.106:993.https://kmapp9.xyz/#/login4.www.akhyiva.c;5.WXL3cJrAX83GsSYe8CP6bD9xGKpr36cGL不法分子使用的抖音号为：28604118735请广大投资者注意识别相关非法人员、网站和各类手机APP或软件，谨防诈骗，保护好个人信息，通过正规渠道进行投资理财，避免高收益诱惑而造成资金损失。我司对此郑重声明：（一）我司和我司员工未参与上述非法证券活动，上述活动系不法分子冒充我司和我司员工名义进行，与我司及我司员工无关。（二）我司炒股软件为手机金太阳APP、金太阳网上交易软件，请广大投资者通过我司官网等正规渠道下载，切勿通过不明网址、二维码进行交易或者下载软件。（三）我司从未以个人名义开展投资咨询业务或服务。任何以个人名义开展投资咨询、收取会员费、承诺收益、盈利分成等，均属非法投资咨询活动。（四）我司开展相关业务从未要求投资者将有关款项支付到任何个人或者对公账户。任何以我司工作人员个人身份开展业务并要求投资者支付到个人或者对公账户的行为，均属违法。（五）我司全国统一服务电话为95536，公司网站为www.guosen.com.cn，微信号为gx002736，官方微博为https://weibo.com/u/5053736747，投资者可以通过上述渠道或者亲临我司当地营业部对工作人员的身份进行核实和提出业务咨询。（六）对任何冒充我司或我司工作人员的非法行为，我司将采取一切必要之法律手段以追究其责任。敬请投资者明辨真伪，警惕此类非法证券活动，切实保护自身利益，谨防受骗，同时欢迎广大投资者积极向我司举报有关非法活动线索。特此公告！                                国信证券股份有限公司                                    2023年6月26日附假冒链接或网页截图： 【免责声明】以上信息仅用于投资者教育，不构成任何投资建议，投资者不应以该等信息取代其独立判断或仅根据该等信息做出决策。国信证券力求该宣传内容的准确可靠，但对其准确性或完整性不作保证，亦不对使用该信息而引发或可能引发的损失承担任何责任。\",\"thumbUpFor\":\"0\",\"title\":\"关于警惕假冒国信证券名义进行非法证券活动的重要公告\",\"typeCode\":302106}";
        //String content = "aaaa";
        String datas = tokenBucketUtils.throttlingCallBack(url, content);
        //datas = null;
        if (StringUtils.isEmpty(datas) || (!datas.contains("\"code\":200") && !datas.contains("\"code\":500200"))) {
            if (!datas.contains("\"code\":500203")){
                //发送至mq
                PushDateDTO pushDateDTO = new PushDateDTO();
                pushDateDTO.setUrl(url);
                pushDateDTO.setContent(content);
                pushDateDTO.setDefaultUrl(false);
                rabbitTemplate.convertAndSend("direct_fail_callBack", "data_fail_callBack", JSONObject.toJSONString(pushDateDTO));
                //rabbitTemplate.convertAndSend(directFcb, keyFcb, pushDateDTO);
                log.info("推送数据至mq："+datas);
                System.out.println("失败a");
            }

            log.info("失败a");
        }else {
            log.info("成功a");
            System.out.println("成功a");
        }
        url = "http://www.baidu.com";
        content = "{\"comments\":\"0\",\"dataId\":\"ea7ab66655644bf49285067e3fe76492\",\"esModelDetailList\":[{\"position\":{},\"word\":\"尊敬的广大投资者：\\n\\n\\n我司近期发现有不法分子假冒国信证券名义进行非法证券活动。主要方式为：不法分子通过抖音等网络平台或其他方式联系投资者，向投资者发送假冒我司的网站链接、软件APP，或者冒充我司员工，引诱投资者转账或者充值，随后资金无法取出。\\n\\n假冒下载链接或网页为：\\n\\n1.6828119.com/index/login\\n\\n 2.43.136.30.106:99\\n\\n3.https://kmapp9.xyz/#/login\\n\\n4.www.akhyiva.c\\n\\n;5.WXL3cJrAX83GsSYe8CP6bD9xGKpr36cGL\\n\\n不法分子使用的抖音号为：28604118735\\n\\n请广大投资者注意识别相关非法人员、网站和各类手机APP或软件，谨防诈骗，保护好个人信息，通过正规渠道进行投资理财，避免高收益诱惑而造成资金损失。\\n\\n我司对此郑重声明：\\n\\n（一）我司和我司员工未参与上述非法证券活动，上述活动系不法分子冒充我司和我司员工名义进行，与我司及我司员工无关。\\n\\n（二）我司炒股软件为手机金太阳APP、金太阳网上交易软件，请广大投资者通过我司官网等正规渠道下载，切勿通过不明网址、二维码进行交易或者下载软件。\\n\\n（三）我司从未以个人名义开展投资咨询业务或服务。任何以个人名义开展投资咨询、收取会员费、承诺收益、盈利分成等，均属非法投资咨询活动。\\n\\n（四）我司开展相关业务从未要求投资者将有关款项支付到任何个人或者对公账户。任何以我司工作人员个人身份开展业务并要求投资者支付到个人或者对公账户的行为，均属违法。\\n\\n（五）我司全国统一服务电话为95536，公司网站为www.guosen.com.cn，微信号为gx002736，官方微博为https://weibo.com/u/5053736747，投资者可以通过上述渠道或者亲临我司当地营业部对工作人员的身份进行核实和提出业务咨询。\\n\\n（六）对任何冒充我司或我司工作人员的非法行为，我司将采取一切必要之法律手段以追究其责任。\\n\\n敬请投资者明辨真伪，警惕此类非法证券活动，切实保护自身利益，谨防受骗，同时欢迎广大投资者积极向我司举报有关非法活动线索。\\n\\n特此公告！\\n\\n \\n\\n                               国信证券股份有限公司\\n\\n                                    2023年6月26日\\n\\n附假冒链接或网页截图：\\n\\n \\n\\n\\n\\n\\n\\n\\n\\n【免责声明】以上信息仅用于投资者教育，不构成任何投资建议，投资者不应以该等信息取代其独立判断或仅根据该等信息做出决策。国信证券力求该宣传内容的准确可靠，但对其准确性或完整性不作保证，亦不对使用该信息而引发或可能引发的损失承担任何责任。\\n\\n\\n\"}],\"forwardingNumber\":\"0\",\"hitUrl\":\"https://www.guosen.com.cn/gs/edu/classroom_detail.html?id=62902\",\"id\":\"e072315045125ef8be031767ed6500cf\",\"imgList\":[],\"lookingAtTheNumber\":\"0\",\"moduleName\":\"XH-168776942323748-3_复制\",\"readTheNumber\":\"0\",\"screenTime\":\"2023-09-26 10:06:47\",\"taskId\":\"81\",\"text\":\"尊敬的广大投资者：我司近期发现有不法分子假冒国信证券名义进行非法证券活动。主要方式为：不法分子通过抖音等网络平台或其他方式联系投资者，向投资者发送假冒我司的网站链接、软件APP，或者冒充我司员工，引诱投资者转账或者充值，随后资金无法取出。假冒下载链接或网页为：1.6828119.com/index/login 2.43.136.30.106:993.https://kmapp9.xyz/#/login4.www.akhyiva.c;5.WXL3cJrAX83GsSYe8CP6bD9xGKpr36cGL不法分子使用的抖音号为：28604118735请广大投资者注意识别相关非法人员、网站和各类手机APP或软件，谨防诈骗，保护好个人信息，通过正规渠道进行投资理财，避免高收益诱惑而造成资金损失。我司对此郑重声明：（一）我司和我司员工未参与上述非法证券活动，上述活动系不法分子冒充我司和我司员工名义进行，与我司及我司员工无关。（二）我司炒股软件为手机金太阳APP、金太阳网上交易软件，请广大投资者通过我司官网等正规渠道下载，切勿通过不明网址、二维码进行交易或者下载软件。（三）我司从未以个人名义开展投资咨询业务或服务。任何以个人名义开展投资咨询、收取会员费、承诺收益、盈利分成等，均属非法投资咨询活动。（四）我司开展相关业务从未要求投资者将有关款项支付到任何个人或者对公账户。任何以我司工作人员个人身份开展业务并要求投资者支付到个人或者对公账户的行为，均属违法。（五）我司全国统一服务电话为95536，公司网站为www.guosen.com.cn，微信号为gx002736，官方微博为https://weibo.com/u/5053736747，投资者可以通过上述渠道或者亲临我司当地营业部对工作人员的身份进行核实和提出业务咨询。（六）对任何冒充我司或我司工作人员的非法行为，我司将采取一切必要之法律手段以追究其责任。敬请投资者明辨真伪，警惕此类非法证券活动，切实保护自身利益，谨防受骗，同时欢迎广大投资者积极向我司举报有关非法活动线索。特此公告！                                国信证券股份有限公司                                    2023年6月26日附假冒链接或网页截图： 【免责声明】以上信息仅用于投资者教育，不构成任何投资建议，投资者不应以该等信息取代其独立判断或仅根据该等信息做出决策。国信证券力求该宣传内容的准确可靠，但对其准确性或完整性不作保证，亦不对使用该信息而引发或可能引发的损失承担任何责任。\",\"thumbUpFor\":\"0\",\"title\":\"关于警惕假冒国信证券名义进行非法证券活动的重要公告\",\"typeCode\":302106}";
        //String content = "aaaa";
        datas = tokenBucketUtils.throttlingCallBack(url, content);
        //datas = null;
        if (StringUtils.isEmpty(datas) || (!datas.contains("\"code\":200") && !datas.contains("\"code\":500200"))) {
            //发送至mq
            PushDateDTO pushDateDTO = new PushDateDTO();
            pushDateDTO.setUrl(url);
            pushDateDTO.setContent(content);
            pushDateDTO.setDefaultUrl(false);
            rabbitTemplate.convertAndSend("direct_fail_callBack", "data_fail_callBack", JSONObject.toJSONString(pushDateDTO));
            //rabbitTemplate.convertAndSend(directFcb, keyFcb, pushDateDTO);
            log.info("推送数据至mq："+datas);
            System.out.println("失败a");
            log.info("失败a");
        }else {
            log.info("成功a");
            System.out.println("成功a");
        }
        return "SUCCESS";
    }

}
