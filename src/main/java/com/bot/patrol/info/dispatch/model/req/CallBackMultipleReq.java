package com.bot.patrol.info.dispatch.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 *
 */
@ApiModel(value="微信回调数据",description="微信回调数据")
@Data
public class CallBackMultipleReq {

    @ApiModelProperty(value="key",name="key")
    private String key;
    @ApiModelProperty(value="数据集合",name="数据集合")
    List<CallBackReq> callBackReqList;



}
