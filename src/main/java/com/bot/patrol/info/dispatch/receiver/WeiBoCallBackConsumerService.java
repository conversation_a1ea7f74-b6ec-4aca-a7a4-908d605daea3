package com.bot.patrol.info.dispatch.receiver;

import com.alibaba.fastjson.JSONObject;
import com.bot.patrol.info.dispatch.entity.TaskInfo;
import com.bot.patrol.info.dispatch.infrastructure.constants.CommonResult;
import com.bot.patrol.info.dispatch.infrastructure.constants.EntityTypeEnum;
import com.bot.patrol.info.dispatch.infrastructure.utils.OkHttpUtils;
import com.bot.patrol.info.dispatch.infrastructure.utils.TokenBucketUtils;
import com.bot.patrol.info.dispatch.model.ESModel;
import com.bot.patrol.info.dispatch.model.ESModelDetail;
import com.bot.patrol.info.dispatch.model.IdUrlModel;
import com.bot.patrol.info.dispatch.model.Position;
import com.bot.patrol.info.dispatch.model.dto.PushDateDTO;
import com.bot.patrol.info.dispatch.model.req.CallBackReq;
import com.bot.patrol.info.dispatch.model.req.TaskInfoSearchReq;
import com.bot.patrol.info.dispatch.model.req.WeiBoCallBackReq;
import com.bot.patrol.info.dispatch.service.AsyncTask;
import com.bot.patrol.info.dispatch.service.HttpApiService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 处理机审结果
 */

@Component
@Slf4j
public class WeiBoCallBackConsumerService {
    @Autowired
    public HttpApiService httpApiService;
    @Value("${internalSystemAddress}")
    private String internalSystemAddress;
    /*@Autowired
    private AsyncTask asyncTask;*/
    @Autowired
    private TokenBucketUtils tokenBucketUtils;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Value("${customs.props.customer-direct-fail-callback}")
    private String directFcb;
    @Value("${customs.props.customer-key-fail-callback}")
    private String keyFcb;

    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = "${customs.weibo.customer-rabbitmq-queue}", autoDelete = "false"),
                    exchange = @Exchange(value = "${customs.weibo.customer-rabbitmq-direct}", durable = "true", type = ExchangeTypes.DIRECT),
                    key = "${customs.weibo.customer-rabbitmq-key}"
            ), concurrency = "50", ackMode = "MANUAL"
    )
    @RabbitHandler
    public void consumeMessage(Message message, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        String str = new String(message.getBody(), "utf-8");
        try {
            /**
             * todo
             * 需要根据账号查询所属任务，多次推送
             */
            WeiBoCallBackReq callBackReq = JSONObject.parseObject(str, WeiBoCallBackReq.class);
            TaskInfoSearchReq taskInfoSearchReq = new TaskInfoSearchReq();
            taskInfoSearchReq.setAppUrl(callBackReq.getSid());
            taskInfoSearchReq.setAppType(EntityTypeEnum.AppTypeEnum.WEBO.getCode());
            CommonResult commonResult = httpApiService.listTaskByOrderUrl(taskInfoSearchReq);
            log.info("查询所属任务"+commonResult);
            List<TaskInfo> list = new ArrayList<>();
            if (200 == commonResult.getCode()) {
                if (ObjectUtils.isNotEmpty(commonResult.getData())) {
                    list = (List<TaskInfo>) commonResult.getData();
                }
            }

            for (int i = 0; i < list.size(); i++) {
                TaskInfo taskInfo = JSONObject.parseObject(JSONObject.toJSONString(list.get(i)), TaskInfo.class);
                System.out.println("任务数据" + JSONObject.toJSONString(taskInfo));
                ESModel esModel = new ESModel();
                esModel.setProjectId(taskInfo.getTiPiSid());
//                esModel.setCompanyId(taskInfo.getTiCpiSid());
                esModel.setAppId(taskInfo.getTiExternalId());
                esModel.setDataId(callBackReq.getId()+taskInfo.getSid());
                esModel.setCaseId(taskInfo.getTiCrawlerId());
                esModel.setTaskId(taskInfo.getTiExternalId());
                esModel.setHitUrl(callBackReq.getUrl());
                esModel.setScreenTime(callBackReq.getCreatTime());
                esModel.setEventId(null);
                esModel.setSearchWordId(null);
                esModel.setModuleName(null);
                if (!callBackReq.getImgUrls().equals("无")){
                    String[] sp=callBackReq.getImgUrls().split(",");
                    esModel.setImgList(Arrays.asList(sp));
                }
                esModel.setId(taskInfo.getSid());
                esModel.setTypeCode(taskInfo.getTiType());
                esModel.setCallBackUrl(taskInfo.getTiExternalCallBackUrl());
                esModel.setComments(callBackReq.getCommentNums());
                esModel.setForwardingNumber("0");
                esModel.setThumbUpFor(callBackReq.getLikeNums());
                esModel.setForwardingNumber(callBackReq.getRetweetNums());
                esModel.setComments(callBackReq.getCommentNums());
                esModel.setWebPdfSnapshot(callBackReq.getSnapshotUrl());
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(callBackReq));
                jsonObject.remove("title");
                jsonObject.remove("hitUrl");
//                List<String> im = new ArrayList<>();
//                String imgUrls = jsonObject.getString("imgUrls");
//                if (StringUtils.isNotEmpty(imgUrls)) {
//                    String[] imgUrl = imgUrls.split(",");
//                    im.addAll(Arrays.asList(imgUrl));
//                }
//                esModel.setImgList(im);
                jsonObject.remove("imgUrls");
//                String videoUrls = jsonObject.getString("videoUrls");
//                List<String> videoList = new ArrayList<>();
//                if (StringUtils.isNotEmpty(videoUrls)) {
//                    String regex = "http.*?,video";
//                    Pattern pattern = Pattern.compile(regex);
//                    Matcher matcher = pattern.matcher(videoUrls);
//                    while (matcher.find()) {
//                        // 获取匹配到的子字符串
//                        String match = matcher.group();
//                        videoList.add(match);
//                    }
//                }
//                esModel.setVideoList(videoList);
//                jsonObject.remove("videoUrls");
                Map<String, Object> jsonMap = JSONObject.toJavaObject(jsonObject, Map.class);
                List<ESModelDetail> esModelDetails = new ArrayList<>();
                StringBuffer stringBuffer = new StringBuffer();
                for (String key : jsonMap.keySet()) {
                    Object value = jsonMap.get(key);
                    ESModelDetail esModelDetail = new ESModelDetail();
                    esModelDetail.setWord(value.toString());
                    Position position = new Position();
                    esModelDetail.setPosition(position);
                    esModelDetails.add(esModelDetail);
                    stringBuffer.append(value);
                }
                esModel.setText(callBackReq.getContentByText());
//                esModel.setAttachmentList(new ArrayList<>());
                esModel.setEsModelDetailList(esModelDetails);

                String url = taskInfo.getTiExternalCallBackUrl();
                String content = JSONObject.toJSONString(esModel);
                String datas = null;
                boolean defaultUrl = false;
                if (StringUtils.isNotEmpty(url)) {
                    //调用回调pushData：mq消费
                    datas = tokenBucketUtils.throttlingCallBack(url, content);
                } else {
                    defaultUrl = true;
                    url = internalSystemAddress + "api/third/patrolAudit";
                    datas = OkHttpUtils.httpPostJson(url, content);
                }
                if (StringUtils.isEmpty(datas) || (!datas.contains("\"code\":200") && !datas.contains("\"code\":500200") && !datas.contains("\"code\":500203"))) {
                    //发送至mq
                    PushDateDTO pushDateDTO = new PushDateDTO();
                    pushDateDTO.setUrl(url);
                    pushDateDTO.setContent(content);
                    pushDateDTO.setDefaultUrl(defaultUrl);
                    rabbitTemplate.convertAndSend(directFcb, keyFcb, JSONObject.toJSONString(pushDateDTO));
                    log.warn("推送巡查数据失败：{}" , datas);
                } else {
                    log.info("推送巡查数据成功：{}" , content);
                }
            }
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            channel.basicNack(deliveryTag, false, true);
            e.printStackTrace();
        }
        log.info("收到微博的消息:{}", str);
    }

}

