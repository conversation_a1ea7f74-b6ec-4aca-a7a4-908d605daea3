package com.bot.patrol.info.dispatch.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
* <AUTHOR>
* @date 2022-03-03
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("quartz_log")
public class QuartzLog implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(value = "baen_name")
    private String baenName;

    @TableField(value = "create_time", insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;

    @TableField(value = "cron_expression")
    private String cronExpression;

    @TableField(value = "exception_detail")
    private String exceptionDetail;

    @TableField(value = "is_success")
    private Boolean isSuccess;

    @TableField(value = "job_name")
    private String jobName;

    @TableField(value = "method_name")
    private String methodName;

    @TableField(value = "params")
    private String params;

    @TableField(value = "time")
    private Long time;


}
