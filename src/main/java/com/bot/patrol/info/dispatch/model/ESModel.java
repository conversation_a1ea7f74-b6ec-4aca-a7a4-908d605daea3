package com.bot.patrol.info.dispatch.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
@ApiModel(value="巡查数据返回值",description="巡查数据返回值")
@Data
public class ESModel {
    @ApiModelProperty(value="数据id",name="数据id")
    private String dataId;
    @ApiModelProperty(value="项目id",name="项目id")
    private String projectId;
    @ApiModelProperty(value="公司id",name="公司id")
    private String companyId;
    @ApiModelProperty(value="应用id",name="应用id")
    private String appId;
    @ApiModelProperty(value="任务id",name="任务id")
    private String taskId;
    @ApiModelProperty(value="脚本id",name="脚本id")
    private String caseId;
    @ApiModelProperty(value="命中url",name="命中url")
    private String hitUrl;
    @ApiModelProperty(value="截屏时间",name="截屏时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime screenTime;
    @ApiModelProperty(value="事件id",name="事件id")
    private String eventId;
    @ApiModelProperty(value="搜索词id",name="搜索词id")
    private String searchWordId;
    @ApiModelProperty(value="模块名称",name="模块名称")
    private String moduleName;
    @ApiModelProperty(value="文本",name="文本")
    private String text;
    @ApiModelProperty(value="url集合",name="url集合")
    private List<String> imgList;
    @ApiModelProperty(value="id",name="id")
    private String id;
    @ApiModelProperty(value="文本",name="文本")
    private Integer typeCode;
    @ApiModelProperty(value="回调地址",name="回调地址")
    private String callBackUrl;
    @ApiModelProperty(value="文本明细",name="文本明细")
    private List<ESModelDetail> esModelDetailList;
    @ApiModelProperty(value="阅读数",name="阅读数")
    private String readTheNumber;
    @ApiModelProperty(value="点赞数",name="点赞数")
    private String thumbUpFor;
    @ApiModelProperty(value="在看数",name="在看数")
    private String lookingAtTheNumber;
    @ApiModelProperty(value="转发数",name="转发数")
    private String forwardingNumber;
    @ApiModelProperty(value="评论数",name="评论数")
    private String comments;
    @ApiModelProperty(value="标题",name="标题")
    private String title;
    @ApiModelProperty(value="pdf地址",name="pdf地址")
    private String webPdfSnapshot;


}
