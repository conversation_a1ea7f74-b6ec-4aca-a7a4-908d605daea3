package com.bot.patrol.info.dispatch.infrastructure.utils;

import com.alibaba.fastjson.JSONObject;
import okhttp3.*;
import org.checkerframework.checker.units.qual.C;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class DingDingBotUtils {
    private Logger log = LoggerFactory.getLogger(this.getClass());

    // 配置的群机器人Webhook地址
    private String botUrl;
    // 配置代理服务器
    private String hostname;
    private int port;

    public DingDingBotUtils(String botUrl, String hostname, int port) {
        this.botUrl = botUrl;
        this.hostname = hostname;
        this.port = port;
    }

    // 直接从配置中获取代理信息
    public DingDingBotUtils(String botUrl, boolean byProxy) {
        this.botUrl = botUrl;
        if (byProxy) {
            hostname = System.getProperty("proxyHost");
            port = Integer.valueOf(System.getProperty("proxyPort"));
        }
    }

    /**
     * 发送文字消息
     *
     * @param msg 需要发送的消息
     * @return
     * @throws Exception
     */
    public String sendTextMsg(String msg) throws Exception {
        JSONObject text = new JSONObject();
        text.put("content", msg);
        List<String> lsi =new ArrayList<>();
        lsi.add("@all");
        text.put("mentioned_list",lsi);
        JSONObject reqBody = new JSONObject();
        reqBody.put("msgtype", "text");
        reqBody.put("text", text);
        reqBody.put("safe", 0);

        return callWeChatBot(reqBody.toString());
    }

    /**
     * 调用群机器人
     *
     * @param reqBody 接口请求参数
     * @throws Exception 可能有IO异常
     */
    public String callWeChatBot(String reqBody) throws Exception {
        log.info("请求参数：" + reqBody);

        // 构造RequestBody对象，用来携带要提交的数据；需要指定MediaType，用于描述请求/响应 body 的内容类型
        MediaType contentType = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(contentType, reqBody);

        // 调用群机器人
        String respMsg = okHttp(body, botUrl);

        if ("0".equals(respMsg.substring(11, 12))) {
            log.info("向群发送消息成功！");
        } else {
            log.info("请求失败！");
            // 发送错误信息到群
            sendTextMsg("群机器人推送消息失败，错误信息：\n" + respMsg);
        }
        return respMsg;
    }

    /**
     *
     * @param body 携带需要提交的数据
     * @param url 请求地址
     * @return
     * @throws Exception
     */
    public String okHttp(RequestBody body, String url) throws Exception {
        // 构造和配置OkHttpClient
        OkHttpClient client;
        if(hostname != null && port != 0){
            client = new OkHttpClient.Builder()
                    .proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(hostname, port))) // 内网使用代理，不需要可注释
                    .connectTimeout(10, TimeUnit.SECONDS) // 设置连接超时时间
                    .readTimeout(20, TimeUnit.SECONDS) // 设置读取超时时间
                    .build();
        } else{
            client = new OkHttpClient.Builder()
                    .connectTimeout(10, TimeUnit.SECONDS) // 设置连接超时时间
                    .readTimeout(20, TimeUnit.SECONDS) // 设置读取超时时间
                    .build();
        }

        // 构造Request对象
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("cache-control", "no-cache") // 响应消息不缓存
                .build();

        // 构建Call对象，通过Call对象的execute()方法提交异步请求
        Response response = null;
        try {
            response = client.newCall(request).execute();
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 请求结果处理
        byte[] datas = response.body().bytes();
        String respMsg = new String(datas);
        log.info("返回结果：" + respMsg);

        return respMsg;
    }

    public static void main(String[] args) throws Exception {
        String dingdingBotUrl = "https://oapi.dingtalk.com/robot/send?access_token=87ed27fd7095a893cb24a0470ff0ad71f91c5394ada701e38a880cec8051818e";
        DingDingBotUtils dingChatBot = new DingDingBotUtils(dingdingBotUrl, false);
        dingChatBot.sendTextMsg("测试一下");
    }
}
