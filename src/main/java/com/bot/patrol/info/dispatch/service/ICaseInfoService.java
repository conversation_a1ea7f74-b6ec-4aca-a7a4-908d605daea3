package com.bot.patrol.info.dispatch.service;

import com.bot.patrol.info.dispatch.entity.CaseInfo;
import com.bot.patrol.info.dispatch.model.req.CaseInfoUpdateReq;

import java.util.List;

public interface ICaseInfoService {
    int add(CaseInfo caseInfo);
    int update(CaseInfo caseInfo);
    CaseInfo selectBySid(String sid);
    void deleteByTaskId(String taskId);
    List<CaseInfo> selectByTaskSid(String taskId);
}
