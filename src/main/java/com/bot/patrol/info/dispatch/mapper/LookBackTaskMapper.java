package com.bot.patrol.info.dispatch.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bot.patrol.info.dispatch.entity.LookBackTask;
import com.bot.patrol.info.dispatch.entity.LookBackUrlList;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface LookBackTaskMapper extends BaseMapper<LookBackTask> {
    default List<LookBackTask> selectByState(int state){
        return selectList(new LambdaQueryWrapper<LookBackTask>().eq(LookBackTask::getLbtState, state));
    }

    default LookBackTask selectByTaskNo(String taskNo){
        return selectOne(new LambdaQueryWrapper<LookBackTask>().eq(LookBackTask::getLbtSno, taskNo));
    }

    default int updateEntity(LookBackTask lookBackTask){
        return updateById(lookBackTask);
    }
}
