package com.bot.patrol.info.dispatch.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("case_info")
public class CaseInfo {
    // 任务执行用例sid
    @TableId(value = "sid")
    private String sid;

    // 任务执行用例编号
    @TableField(value = "ci_sno")
    private String ciSno;

    // 执行用例名称
    @TableField(value = "ci_case_name")
    private String ciCaseName;

    // 归属行业
    @TableField(value = "ci_industry_type")
    private String ciIndustryType;

    // 项目id
    @TableField(value = "ci_pi_sid")
    private String ciPiSid;

    // 应用id
    @TableField(value = "ci_ai_sid")
    private String ciAiSid;

    // 用户id
    @TableField(value = "ci_cpi_sid")
    private String ciCpiSid;

    // 任务用户实体名称
    @TableField(value = "ci_cpi_name")
    private String ciCpiName;

    // 关联任务sid
    @TableField(value = "ci_ti_sid")
    private String ciTiSid;

    // 关联任务编号
    @TableField(value = "ci_ti_sno")
    private String ciTiSno;

    // 任务类型
    @TableField(value = "ci_ti_type")
    private Integer ciTiType;

    // 关联订单id
    @TableField(value = "ci_ol_sid")
    private String ciOlSid;

    // 关联订单编号
    @TableField(value = "ci_ol_sno")
    private String ciOlSno;

    // 关联执行脚本id
    @TableField(value = "ci_si_sid")
    private String ciSiSid;

    // 执行用例脚本类型
    @TableField(value = "ci_si_type")
    private Integer ciSiType;

    // 执行用例步骤
    @TableField(value = "ci_steps")
    private String ciSteps;

    // 执行用例应用包名
    @TableField(value = "ci_ai_package")
    private String ciAiPackage;

    // 执行用例应用类型
    @TableField(value = "ci_ai_type")
    private Integer ciAiType;

    // 执行用例应用名称
    @TableField(value = "ci_ai_name")
    private String ciAiName;

    // 应用地址
    @TableField(value = "ci_ai_url")
    private String ciAiUrl;

    // 应用获取来源
    @TableField(value = "ci_ai_get_origin")
    private String ciAiGetOrigin;

    // 任务运行类型
    @TableField(value = "ci_run_type")
    private Integer ciRunType;

    // 任务周期运行类型
    @TableField(value = "ci_run_cycle")
    private Integer ciRunCycle;

    // 任务周期运行值
    @TableField(value = "ci_cycle_num")
    private Integer ciCycleNum;

    // 执行用例指定执行器id
    @TableField(value = "ci_aci_sid")
    private String ciAciSid;

    // 执行用例运行时执行器id
    @TableField(value = "ci_runtime_aci_sid")
    private String ciRuntimeAciSid;

    // 名称
    @TableField(value = "ci_aci_name")
    private String ciAciName;

    // 执行器队列标识
    @TableField(value = "ci_aci_queue_flag")
    private String ciAciQueueFlag;

    // 执行器访问类型
    @TableField(value = "ci_aci_conn_type")
    private Integer ciAciConnType;

    // 执行器设备标识
    @TableField(value = "ci_aci_device")
    private String ciAciDevice;

    // 计划开始时间
    @TableField(value = "ci_start_time")
    private LocalDateTime ciStartTime;

    // 计划结束时间
    @TableField(value = "ci_end_time")
    private LocalDateTime ciEndTime;

    // 执行器设备网络标识
    @TableField(value = "ci_aci_device_wifi")
    private String ciAciDeviceWifi;

    // 操作人
    @TableField(value = "ci_operator_sid",fill = FieldFill.INSERT_UPDATE)
    private String ciOperatorSid;

    // 执行用例创建时间
    @TableField(value = "ci_create_time")
    private LocalDateTime ciCreateTime;

    // 执行用例最后更新时间
    @TableField(value = "ci_update_time")
    private LocalDateTime ciUpdateTime;

    // 运行情况 0-停用 1-启用
    @TableField(value = "ci_state")
    private Integer ciState;

    // 执行用例状态
    @TableField(value = "ci_status")
    private Integer ciStatus;

    // 执行任务优先级
    @TableField(value = "ci_ti_priority")
    private Integer ciTiPriority;

    // 执行优先级
    @TableField(value = "ci_priority")
    private Integer ciPriority;

    // 最后执行开始时间
    @TableField(value = "ci_last_run_start_time")
    private LocalDateTime ciLastRunStartTime;

    // 最后执行结束时间
    @TableField(value = "ci_last_run_end_time")
    private LocalDateTime ciLastRunEndTime;

    @TableField(value = "ci_aci_service_flag")
    private String ciAciServiceFlag;
}
