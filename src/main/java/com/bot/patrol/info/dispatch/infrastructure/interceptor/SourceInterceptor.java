package com.bot.patrol.info.dispatch.infrastructure.interceptor;

import com.bot.patrol.info.dispatch.infrastructure.constants.AppConstant;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.BaseGlobalInterceptor;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

@Component
public class SourceInterceptor extends BaseGlobalInterceptor {
    @Override
    protected Response doIntercept(Chain chain) throws IOException {
        Request request = chain.request();

        Request  newReq = request.newBuilder()
                .addHeader(AppConstant.AUTH_TOKEN, "118a10949f28a442efb5dfb60fbccaa9")
                .build();
        return chain.proceed(newReq);
    }
}
