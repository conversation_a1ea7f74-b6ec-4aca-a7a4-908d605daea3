package com.bot.patrol.info.dispatch.service.impl;

import com.bot.patrol.info.dispatch.conver.ActuatorInfoConvert;
import com.bot.patrol.info.dispatch.conver.TaskInfoConvert;
import com.bot.patrol.info.dispatch.entity.ext.ActuatorInfo;
import com.bot.patrol.info.dispatch.infrastructure.constants.AppConstant;
import com.bot.patrol.info.dispatch.infrastructure.utils.JsonUtils;
import com.bot.patrol.info.dispatch.infrastructure.utils.OkHttpUtils;
import com.bot.patrol.info.dispatch.service.IActuatorService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONNull;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class ActuatorInfoServiceImpl implements IActuatorService {
    private final String actuatorInterfaceUrl = AppConstant.DATA_CENTER_HOST_URL + "/task-info/getRunTaskBySid";

    @Override
    public List<ActuatorInfo> getByRunCondition(String serviceFlag, String packageName) {
        JSONObject getActuatorJo = new JSONObject();
        getActuatorJo.put("serviceFlag", serviceFlag);
        getActuatorJo.put("packageName", packageName);
        List<ActuatorInfo> retList = null;
        String ret = OkHttpUtils.httpPostJson(actuatorInterfaceUrl, AppConstant.DATA_CENTER_TOKEN_HEADER, getActuatorJo.toString());
        if (Objects.nonNull(ret) && JSONObject.fromObject(ret).getInt("code") == 200) {
            JSONObject retData = JSONObject.fromObject(ret);
            if (!(retData.getJSONObject("data").equals(JSONNull.getInstance()) && retData.getString("data") != "")) {
                List<Map<String, Object>> aciList = JsonUtils.jsonToList(JSONObject.fromObject(ret).getString("data"));
                retList = new ArrayList<>();
                if (aciList.size() > 0) {
                    for (int i = 0; i < aciList.size(); i++) {
                        ActuatorInfo actuatorInfo = ActuatorInfoConvert.INSTANCE.convert(JsonUtils.jsonToMap(JSONObject.fromObject(ret).getString("data")));
                        retList.add(actuatorInfo);
                    }
                }
            }
        }
        return retList;
    }
}
