package com.bot.patrol.info.dispatch.receiver;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bot.patrol.info.dispatch.conver.CaseInfoConvert;
import com.bot.patrol.info.dispatch.entity.CaseInfo;
import com.bot.patrol.info.dispatch.entity.TaskInfo;
import com.bot.patrol.info.dispatch.infrastructure.constants.CommonResult;
import com.bot.patrol.info.dispatch.infrastructure.constants.EntityTypeEnum;
import com.bot.patrol.info.dispatch.infrastructure.utils.QyWeChartUtils;
import com.bot.patrol.info.dispatch.infrastructure.utils.RedisUtils;
import com.bot.patrol.info.dispatch.infrastructure.utils.WeChatBotUtils;
import com.bot.patrol.info.dispatch.model.req.*;
import com.bot.patrol.info.dispatch.schediule.RunTask;
import com.bot.patrol.info.dispatch.service.HttpApiService;
import com.bot.patrol.info.dispatch.service.ICaseInfoService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
@Transactional
public class ActuatorConsumerService {


    @Autowired
    RabbitTemplate rabbitTemplate;

    @Autowired
    private ICaseInfoService iCaseInfoService;

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private HttpApiService httpApiService;
    @RabbitListener(
            bindings=@QueueBinding(
                    value=@Queue(value="${customs.task.rabbitmq-queue-actuator-callback}",autoDelete="false"),
                    exchange=@Exchange(value="${customs.task.rabbitmq-direct-actuator-callback}",durable="true", type= ExchangeTypes.DIRECT),
                    key="${customs.task.rabbitmq-key-actuator-callback}"
            ), concurrency = "50", ackMode = "MANUAL"
    )
    @RabbitHandler
    public void consumeMessage(Message message, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        String str = new String(message.getBody(), "utf-8");
        try {
            JSONObject jsonObject=JSONObject.parseObject(str);
            String id=jsonObject.getString("id");
            String taskId=jsonObject.getString("taskId");
            //0-停用 stop 1-启用 run
            int type=jsonObject.getIntValue("type");
            String actuatorId=jsonObject.getString("actuatorId");
            /**
             * TERMINATED：被终止   COMPLETED：完成    RUNNING：运行中
             */
            String status=jsonObject.getString("status");
            if("COMPLETED".equals(status)){
                CaseInfoUpdateReq caseInfoUpdateReq=new CaseInfoUpdateReq();
                caseInfoUpdateReq.setSid(id);
                caseInfoUpdateReq.setCiState(1);
                caseInfoUpdateReq.setCiStatus(EntityTypeEnum.TaskStatusTypeEnum.Stop.getCode());
                caseInfoUpdateReq.setCiLastRunEndTime(LocalDateTime.now());
                CommonResult commonResult1=httpApiService.caseInfoUpdate(caseInfoUpdateReq);
                log.info(JSONObject.toJSONString(commonResult1));
                CaseInfo dispatchCaseInfo = CaseInfoConvert.INSTANCE.convert(caseInfoUpdateReq);
                iCaseInfoService.update(dispatchCaseInfo);
                TaskInfoUpdateReq taskInfoUpdateReq=new TaskInfoUpdateReq();
                taskInfoUpdateReq.setSid(taskId);
                List<CaseInfo> caseInfoList = iCaseInfoService.selectByTaskSid(taskId);
                Optional<CaseInfo> optional = caseInfoList.stream().filter(p ->
                        p.getCiStatus() == EntityTypeEnum.TaskStatusTypeEnum.Start.getCode()).findFirst();
                if (!optional.isPresent()) {
                    taskInfoUpdateReq.setTiStatus(EntityTypeEnum.TaskStatusTypeEnum.Stop.getCode());
                }
                CommonResult commonResult=httpApiService.update(taskInfoUpdateReq);
                log.info(JSONObject.toJSONString(commonResult));
                //判断执行器是否有排队数据 如果有直接执行
                String link=redisUtils.get("Link-"+id);
                if(StringUtils.isNotEmpty(link)){
                    redisUtils.deleteByPrex("Link-"+id);
                    if(type==0) {
                        JSONArray jsonArray = new JSONArray();
                        jsonArray.add(id);
                        RunTask.run(jsonArray.toJSONString());
                    }
                }
            }
            if("TERMINATED".equals(status)){
                CaseInfoUpdateReq caseInfoUpdateReq=new CaseInfoUpdateReq();
                caseInfoUpdateReq.setSid(id);
                if(type==0){
                    caseInfoUpdateReq.setCiStatus(EntityTypeEnum.TaskStatusTypeEnum.Start.getCode());
                    caseInfoUpdateReq.setCiState(1);
                }else{
                    caseInfoUpdateReq.setCiStatus(EntityTypeEnum.TaskStatusTypeEnum.Stop.getCode());
                    caseInfoUpdateReq.setCiState(0);
                }
                CommonResult caseInfoUpdateResult=httpApiService.caseInfoUpdate(caseInfoUpdateReq);
                log.info(JSONObject.toJSONString(caseInfoUpdateResult));
                CaseInfo dispatchCaseInfo = CaseInfoConvert.INSTANCE.convert(caseInfoUpdateReq);
                iCaseInfoService.update(dispatchCaseInfo);
                TaskInfoUpdateReq taskInfoUpdateReq=new TaskInfoUpdateReq();
                taskInfoUpdateReq.setSid(taskId);
                taskInfoUpdateReq.setTiRunWarning(1);
                List<CaseInfo> caseInfoList = iCaseInfoService.selectByTaskSid(taskId);
                Optional<CaseInfo> optional = caseInfoList.stream().filter(p ->
                        p.getCiStatus() == EntityTypeEnum.TaskStatusTypeEnum.Start.getCode()).findFirst();
                if (!optional.isPresent()) {
                    taskInfoUpdateReq.setTiStatus(EntityTypeEnum.TaskStatusTypeEnum.Stop.getCode());
                }else{
                    taskInfoUpdateReq.setTiStatus(EntityTypeEnum.TaskStatusTypeEnum.Start.getCode());
                }
                CommonResult commonResult=httpApiService.update(taskInfoUpdateReq);
                log.info(JSONObject.toJSONString(commonResult));
                Optional<CaseInfo> optionalBySid = caseInfoList.stream().filter(p -> p.getSid().equals(id)).findFirst();
                String caseInfoSno = null;
                if (optionalBySid.isPresent()){
                    caseInfoSno = optionalBySid.get().getCiSno();
                }
                if(type==0) {
                    QyWeChartUtils.sendMsgByText("执行器通知:脚本停止异常，脚本编号为:" + caseInfoSno);
                }else{
                    QyWeChartUtils.sendMsgByText("执行器通知:脚本运行异常，脚本编号为:" + caseInfoSno);
                }
            }
            if("RUNNING".equals(status)){
                CaseInfoUpdateReq caseInfoUpdateReq=new CaseInfoUpdateReq();
                caseInfoUpdateReq.setSid(id);
                caseInfoUpdateReq.setCiState(1);
                caseInfoUpdateReq.setCiStatus(EntityTypeEnum.TaskStatusTypeEnum.Start.getCode());
                caseInfoUpdateReq.setCiLastRunStartTime(LocalDateTime.now());
                CommonResult commonResult1=httpApiService.caseInfoUpdate(caseInfoUpdateReq);
                log.info("更新最后执行开始时间"+JSONObject.toJSONString(commonResult1));
                CaseInfo dispatchCaseInfo = CaseInfoConvert.INSTANCE.convert(caseInfoUpdateReq);
                iCaseInfoService.update(dispatchCaseInfo);
                ActuatorInfoUpdateReq actuatorInfoUpdateReq=new ActuatorInfoUpdateReq();
                actuatorInfoUpdateReq.setSid(actuatorId);
                actuatorInfoUpdateReq.setAciStatus(1);
                CommonResult commonResult=httpApiService.actuatorUpdate(actuatorInfoUpdateReq);
                log.info("更新任务状态"+JSONObject.toJSONString(commonResult));
                if(commonResult.getCode()!=200){
                    log.error("test_ack");
                    channel.basicNack(deliveryTag, false, true);
                }else {
                    TaskInfoSearchReq taskInfoSearchReq=new TaskInfoSearchReq();
                    taskInfoSearchReq.setSid(taskId);
                    CommonResult commonResult2=httpApiService.list(taskInfoSearchReq);
                    List<TaskInfo> list=new ArrayList<>();
                    if(200==commonResult2.getCode()){
                        if(ObjectUtils.isNotEmpty(commonResult2.getData())){
                            list= (List<TaskInfo>) commonResult2.getData();
                        }
                    }
                    if (list.size()>0){
                        TaskInfo taskInfo=JSONObject.parseObject(JSONObject.toJSONString(list.get(0)),TaskInfo.class);
                        String botUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b26ae18c-a406-47c0-ba63-27089b3db6b1";
                        WeChatBotUtils weChatBot = new WeChatBotUtils(botUrl, false);
                        weChatBot.sendTextMsg(taskInfo.getTiSno()+"\n" +
                                taskInfo.getTiCpiName()+"\n" +
                                taskInfo.getTiDescr()+"\n" +
                                "\n" +
                                "脚本已开始执行");
                    }
                }
            }
            //去解锁执行器
            Boolean b=redisUtils.redisTemplate.delete("Lock-"+actuatorId);
            log.info("解锁是否成功"+b);
            channel.basicAck(deliveryTag, false);
        }catch (Exception e){
            log.error(e.getMessage());
            channel.basicNack(deliveryTag, false, true);
            e.printStackTrace();
        }


        log.info("收到的消息:{}",str);
    }

}

