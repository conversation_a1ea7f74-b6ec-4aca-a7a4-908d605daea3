package com.bot.patrol.info.dispatch.infrastructure.utils;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@ConfigurationProperties(prefix = "custom-props")
public class PropertiesUtils {
    private static Map<String, String> props = new HashMap<>(); //接收props里面的属性值
    public static Map<String, String> getProps() {
        return props;
    }
    public void setProps(Map<String, String> props) {
        this.props = props;
    }
}