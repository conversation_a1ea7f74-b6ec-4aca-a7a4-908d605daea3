package com.bot.patrol.info.dispatch.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("look_back_url_list")
public class LookBackUrlList {
    /**
     * sid
     * 回查列表id
     */
    @TableId(value = "sid", type = IdType.ASSIGN_UUID)
    private String sid;
    /**
     * lul_lbt_sid
     * 回查任务id
     */
    @TableField(value = "lul_lbt_sid")
    private String lulLbtSid;
    /**
     * lul_external_id
     * 回查项第三方id
     */
    @TableField(value = "lul_external_id")
    private String lulExternalId;
    /**
     * lul_url
     * 回查项url
     */
    @TableField(value = "lul_url")
    private String lulUrl;
    /**
     * lul_state
     * 回查项状态0-运行1-完成
     */
    @TableField(value = "lul_state")
    private Integer lulState;
    /**
     * lul_create_time
     * 回查列表项创建时间
     */
    @TableField(value = "lul_create_time", insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private String lulCreateTime;
    /**
     * lul_update_time
     * lbt_update_time
     */
    @TableField(value = "lul_update_time", insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private String lulUpdateTime;

}
