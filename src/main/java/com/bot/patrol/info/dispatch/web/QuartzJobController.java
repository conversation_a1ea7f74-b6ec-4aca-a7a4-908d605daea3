package com.bot.patrol.info.dispatch.web;

import com.bot.patrol.info.dispatch.infrastructure.annotation.RequiresPermission;
import com.bot.patrol.info.dispatch.infrastructure.constants.CommonResult;
import com.bot.patrol.info.dispatch.service.IQuartzJobService;
import com.bot.patrol.info.dispatch.model.req.*;
import com.bot.patrol.info.dispatch.model.page.QuartzJobPage;
import com.bot.patrol.info.dispatch.service.ITaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
/**
* <AUTHOR>
* @date 2022-03-03
*/
@Api(tags = "定时任务管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/quartz-job")
public class QuartzJobController {

    private final IQuartzJobService quartzJobService;
    private final ITaskService taskService;

    @PostMapping(value = "/test")
    @ApiOperation(value = "测试", notes = "")
    public CommonResult test(@RequestBody @Valid  TestReq request) {
        taskService.addSchediuleTask(request.getSid());
        return CommonResult.success();
    }

    /**
     * 新增定时任务
     * @param request
     * @return
     */
    @PostMapping(value = "/add")
    @ApiOperation(value = "新增", notes = "")
    @RequiresPermission("quartz.job.add")
    public CommonResult add(@RequestBody @Valid QuartzJobAddReq request) {
    return quartzJobService.add( request);
    }

    @PostMapping(value = "/update")
    @ApiOperation(value = "修改", notes = "")
    @RequiresPermission("quartz.job.update")
    public CommonResult update(@RequestBody @Valid QuartzJobUpdateReq request) {
    return quartzJobService.update( request);
    }


    @PostMapping(value = "/list")
    @ApiOperation(value = "查询列表不带分页", notes = "")
    @RequiresPermission("quartz.job.list")
    public CommonResult list(@RequestBody @Valid QuartzJobSearchReq request) {
    return quartzJobService.list( request);
    }

    @PostMapping(value = "/page")
    @ApiOperation(value = "查询应用带分页", notes = "")
    @RequiresPermission("quartz.job.page")
    public CommonResult page(@RequestBody @Valid QuartzJobPage request) {
    return quartzJobService.page( request);
    }

    @PostMapping(value = "/updateByCronExpression")
    @ApiOperation(value = "修改任务执行时间", notes = "")
    public CommonResult updateByCronExpression(@RequestBody @Valid QuartzJobUpdateReq request) {
        return quartzJobService.updateByCronExpression( request);
    }
}
