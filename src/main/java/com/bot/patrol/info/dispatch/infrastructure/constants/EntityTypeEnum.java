package com.bot.patrol.info.dispatch.infrastructure.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

public class EntityTypeEnum {
    @Getter
    @AllArgsConstructor
    public enum AppTypeEnum {
        WEB(300300, "网页"),
        ANDROID(300301, "安卓"),
        IOS(300302, "IOS"),
        WCAC(300303, "公众号"),
        WCLS(300304,"微信小程序"),
        APLS(300305,"支付宝小程序"),
        BDLS(300306,"百度小程序"),
        API(300307,"API"),
        WEBO(300308,"微博");

        private final int code;
        private final String value;

        //根据key获取枚举
        public static AppTypeEnum getEnumByKey(int key){
            for(AppTypeEnum temp: AppTypeEnum.values()){
                if(temp.getCode() == key){
                    return temp;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum OriginEnum {
        LOCAL_USER(300100, "本地用户"),
        DATEBASE(300101, "应用数据库"),
        COMPANY_USER(300102, "客户方用户"),
        ADMIN_USER(300103, "管理用户"),
        EXT_USER(300104, "外部应用");

        private final int code;
        private final String value;

        //根据key获取枚举
        public static OriginEnum getEnumByKey(int key){
            for(OriginEnum temp: OriginEnum.values()){
                if(temp.getCode() == key){
                    return temp;
                }
            }
            return null;
        }
    }



    @Getter
    @AllArgsConstructor
    public enum ActuatorTypeEnum {
        ANDROID(303101, "安卓手机"),
        IPHONE(303102, "苹果手机"),
        WEB(303103, "网页浏览器"),
        INTERFACE(303104, "接口");

        private final int code;
        private final String value;

        //根据key获取枚举
        public static ActuatorTypeEnum getEnumByKey(int key){
            for(ActuatorTypeEnum temp: ActuatorTypeEnum.values()){
                if(temp.getCode() == key){
                    return temp;
                }
            }
            return null;
        }
    }


    @Getter
    @AllArgsConstructor
    public enum CycleTypeEnum {
        Day(305101, "按天执行"),
        Week(305102, "按周执行"),
        Month(305103, "按月执行"),
        Interval (305104,"时间间隔"),
        Sustain (305105,"持续执行");


        private final int code;
        private final String value;

        //根据key获取枚举
        public static CycleTypeEnum getEnumByKey(int key){
            for(CycleTypeEnum temp: CycleTypeEnum.values()){
                if(temp.getCode() == key){
                    return temp;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum TaskRunTypeEnum {
        INSTANT(306101, "即时执行"),
        TIMING(306102, "定时执行"),
        CYCLE(306103, "周期执行"),
        SUSPEND(306104, "暂停执行"),
        DISCONTINUE(306105, "中止执行");

        private final int code;
        private final String value;

        //根据key获取枚举
        public static TaskRunTypeEnum getEnumByKey(int key) {
            for (TaskRunTypeEnum temp : TaskRunTypeEnum.values()) {
                if (temp.getCode() == key) {
                    return temp;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum TaskStatusTypeEnum {
        Start(307101, "开始"),
        Stop(307102, "停止"),
        Pause(307103, "暂停"),
        Continue(307104, "继续");

        private final int code;
        private final String value;

        //根据key获取枚举
        public static TaskStatusTypeEnum getEnumByKey(int key){
            for(TaskStatusTypeEnum temp: TaskStatusTypeEnum.values()){
                if(temp.getCode() == key){
                    return temp;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum QuartzPushStateEnum {
        WAIT_PUSH(0, "等待开始"),
        PUSHING(1, "推送中"),
        PUSH_END(2, "推送结束");

        private final int code;
        private final String value;

        //根据key获取枚举
        public static QuartzPushStateEnum getEnumByKey(int key){
            for(QuartzPushStateEnum temp: QuartzPushStateEnum.values()){
                if(temp.getCode() == key){
                    return temp;
                }
            }
            return null;
        }
    }
    @Getter
    @AllArgsConstructor
    public enum QuartzWeekEnum {
        MON(2, "MON"),
        TUE(3, "TUE"),
        WED(4, "WED"),
        THU(5, "THU"),
        FRI(6, "FRI"),
        SAT(7, "SAT"),
        SUN(1, "SUN");

        private final int code;
        private final String value;

        //根据key获取枚举
        public static QuartzWeekEnum getEnumByKey(int key){
            for(QuartzWeekEnum temp: QuartzWeekEnum.values()){
                if(temp.getCode() == key){
                    return temp;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum TaskSourceTypeEnum {

        ALPHA_TESTING(302101, "内部测试"),
        PEOPLE_CN(302102, "人民网巡查业务"),
        APP_PATROL(302103, "APP巡查业务"),
        ANNEXED_TERRITORY(302104, "属地巡查业务"),
        XINHUA_NET(302105, "新华网巡查业务"),
        BOTSMART(302106, "内部巡查业务");


        private final int code;
        private final String value;

        //根据key获取枚举
        public static TaskSourceTypeEnum getEnumByKey(int key){
            for(TaskSourceTypeEnum temp: TaskSourceTypeEnum.values()){
                if(temp.getCode() == key){
                    return temp;
                }
            }
            return null;
        }
    }


}
