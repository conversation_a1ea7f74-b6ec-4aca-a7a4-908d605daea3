package com.bot.patrol.info.dispatch.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bot.patrol.info.dispatch.entity.CaseInfo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface CaseInfoMapper extends BaseMapper<CaseInfo> {
    default CaseInfo selectBySid(String sid){
        return selectOne(new LambdaQueryWrapper<CaseInfo>().eq(CaseInfo::getSid, sid));
    }

    default List<CaseInfo> selectByTaskSid(String taskSid){
        return selectList(new LambdaQueryWrapper<CaseInfo>().eq(CaseInfo::getCiTiSid, taskSid));
    }

    default int add(CaseInfo caseInfo){
        return insert(caseInfo);
    }

    default int update(CaseInfo caseInfo){
        return updateById(caseInfo);
    }



    default int deleteByTaskId(String taskId){
        return delete(new LambdaQueryWrapper<CaseInfo>().eq(CaseInfo::getCiTiSid, taskId));
    }
}
