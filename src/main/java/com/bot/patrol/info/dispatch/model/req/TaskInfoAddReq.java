package com.bot.patrol.info.dispatch.model.req;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
* <AUTHOR>
* @date 2022-02-08
*/
@ApiModel(value="任务信息对象新增",description="任务信息")
@Data
public class TaskInfoAddReq {

    // 用户id
    @ApiModelProperty(value="公司id",name="公司id")
    private String tiCpiSid;
    // 任务用户实体名称
    @ApiModelProperty(value="公司名称",name="公司名称")
    private String tiCpiName;
    // 任务描述
    @ApiModelProperty(value="任务描述",name="任务描述")
    private String tiDescr;
    // 归属行业
    @ApiModelProperty(value="归属行业",name="归属行业")
    private String tiIndustryType;
    @ApiModelProperty(value="任务类型0-即时执行 1-按天执行 2-按周执行 3-按月执行",name="任务运行类型")
    private Integer type;
    // 任务运行类型
    @ApiModelProperty(value="任务运行类型 Day(305101, \"按天执行\"),\n" +
            "        Week(305102, \"按周执行\"),\n" +
            "        Month(305103, \"按月执行\"),\n",name="任务运行类型")
    private Integer tiRunType;
    // 任务周期运行类型
    @ApiModelProperty(value="任务周期运行类型Instant(306101, \"即时执行\"),\n" +
            "        Cycle(306103, \"周期执行\"),\n ",name="任务周期运行类型")
    private Integer tiRunCycle;
    // 任务周期运行值
    @ApiModelProperty(value="任务周期运行值",name="任务周期运行值")
    private Integer tiCycleNum;
    // 任务计划开始时间
    @ApiModelProperty(value="任务计划开始时间",name="任务计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tiStartTime;
    // 任务计划结束时间
    @ApiModelProperty(value="任务计划结束时间",name="任务计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tiEndTime;
    // 执行优先级
    @ApiModelProperty(value="执行优先级",name="执行优先级")
    private Integer tiPriority;

    @ApiModelProperty(value="附件地址",name="附件地址")
    private String tiEnclosureUrl;


    private  List<OrderList> orderLists;

    @ApiModelProperty(value="外部id",name="外部id")
    private String tiExternalId;

    @ApiModelProperty(value="爬虫引擎id",name="爬虫引擎id")
    private String tiCrawlerId;

    @ApiModelProperty(value="业务分支1-巡查2-清洗3-巡查加清洗",name="业务分支1-巡查2-清洗3-巡查加清洗")
    private Integer tiTypeChild;

    @ApiModelProperty(value="外部回调地址",name="外部回调地址")
    private String tiExternalCallBackUrl;

    @ApiModelProperty(value="任务类型",name="任务类型")
    @NotNull(message = "不能为空")
    private Integer tiType;

    private Integer tiStatus;

    private Integer tiState;




}
