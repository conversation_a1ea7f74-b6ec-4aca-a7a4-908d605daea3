package com.bot.patrol.info.dispatch.infrastructure.constants;

import com.bot.patrol.info.dispatch.infrastructure.utils.CommUtils;

import java.util.Map;
import java.util.Set;
import org.springframework.beans.factory.annotation.Value;

public interface AppConstant {

    String AAPT_PATH = "/android_tools/27.0.1/aapt";
    String APP_TEMP_PATH = "/app_temp/";
    String APK_TEMP_NAME = "apk_temp.apk";
    String APK_ICON_TEMP_NAME = "apk_temp_icon.png";

    String AUTH_TOKEN = "Auth-Token";
    String WEB_TOKEN = "Web-Token";
    String KEY_PREFIX = "console_";
    String DIRECT_TASK_INIT = "direct_task_init";
    String QUEUE_TASK_INIT = "queue_task_init";
    String ROUTING_KEY_TASK_INIT = "task_init";

    String DIRECT_TASK_TEST = "direct_task_test";
    String QUEUE_TASK_TEST = "queue_task_test";
    String ROUTING_KEY_TASK_TEST = "task_test";

    String DEFAULT_PASSWORD = "botsmart.cn";



    String SMS_SUCCESS = "OK";

    Integer PATROL_NUM = 52;

    String DATA_CENTER_HOST_URL = CommUtils.getCenterUrl();

    Map<String, String> DATA_CENTER_TOKEN_HEADER = CommUtils.getCenterTokenHeader();

    Set<String> WHITE_LIST = Set.of("/auth/captcha", "/auth/verCode", "/auth/login","/auth/webLogin","/callBack/receiveData", "/script-info/get-script-by-sid"
            , "/task-info/get-case", "/task-info/up-case-status", "/task-info/get-sno-by-url", "/doc.html","/favicon.ico","/swagger-resources","/v2/api-docs");
}