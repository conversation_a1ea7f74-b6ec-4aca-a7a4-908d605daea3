package com.bot.patrol.info.dispatch.model.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 *
 */
@ApiModel(value="微信回调数据",description="微信回调数据")
@Data
public class CallBackReq {

    @ApiModelProperty(value="文章唯一标识",name="文章唯一标识")
    private String id;
    @ApiModelProperty(value="任务id(获取数据系统的id)",name="任务id(获取数据系统的id)")
    private String sid;
    @ApiModelProperty(value="文章标题",name="文章标题")
    private String title;
    @ApiModelProperty(value="文章内容(带HTML)",name="文章内容(带HTML)")
    private String contentByHtml;
    @ApiModelProperty(value="文章内容(纯文本)",name="文章内容(纯文本)")
    private String contentByText;
    @ApiModelProperty(value="文章Url",name="文章Url")
    private String url;
    @ApiModelProperty(value="发布时间",name="发布时间")
    private LocalDateTime creatTime;
    @ApiModelProperty(value="阅读数",name="阅读数")
    private String readNums;
    @ApiModelProperty(value="点赞数", name="点赞数")
    private String likeNums;
    @ApiModelProperty(value="评论数", name="评论数")
    private String commentNums;
    @ApiModelProperty(value="在看数", name="在看数")
    private String lookInNums;
    @ApiModelProperty(value="文章内图片地址链接", name="文章内图片链接")
    private List<String> imgUrls;
    @ApiModelProperty(value="文章内视频地址链接", name="文章内视频地址链接")
    private List<String> videoUrls;


}
