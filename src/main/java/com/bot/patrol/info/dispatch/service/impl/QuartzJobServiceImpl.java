package com.bot.patrol.info.dispatch.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bot.patrol.info.dispatch.conver.QuartzJobConvert;
import com.bot.patrol.info.dispatch.entity.QuartzJob;
import com.bot.patrol.info.dispatch.infrastructure.constants.CommonResult;
import com.bot.patrol.info.dispatch.infrastructure.constants.ErrorCodeEnum;
import com.bot.patrol.info.dispatch.manager.QuartzManage;
import com.bot.patrol.info.dispatch.mapper.QuartzJobMapper;
import com.bot.patrol.info.dispatch.model.req.*;
import com.bot.patrol.info.dispatch.model.page.QuartzJobPage;
import com.bot.patrol.info.dispatch.service.IQuartzJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* <AUTHOR>
* @date 2022-03-03
*/
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class QuartzJobServiceImpl implements IQuartzJobService {

    private final QuartzJobMapper quartzJobMapper;
    private final QuartzManage quartzManage;
    @Override
    public CommonResult add(QuartzJobAddReq request) {
        QuartzJob quartzJob = QuartzJobConvert.INSTANCE.convert(request);
        try {
        quartzJobMapper.insert(quartzJob);
        quartzManage.addJob(quartzJob);
        } catch (Exception e) {
        return CommonResult.error(ErrorCodeEnum.CONTENT_REPEAT);
        }
        return CommonResult.success();
    }

    @Override
    public int insert(QuartzJob quartzJob){
        return quartzJobMapper.insert(quartzJob);
    }

    @Override
    public boolean add(QuartzJob quartzJob) {
        try {
            quartzJob.setIsPush(true);
            quartzJobMapper.updateById(quartzJob);
            quartzManage.addJob(quartzJob);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    @Override
    public QuartzJob selectByJobName(String taskId) {
        return quartzJobMapper.selectByJobName(taskId);
    }

    @Override
    public QuartzJob selectNoPushedByJobName(String taskSid) {
        return quartzJobMapper.selectNoPushedByJobName(taskSid);
    }

    @Override
    public List<QuartzJob> selectNoPushed() {
        return quartzJobMapper.selectNoPushedList();
    }

    @Override
    public CommonResult update( QuartzJobUpdateReq request) {
        QuartzJob quartzJob = QuartzJobConvert.INSTANCE.convert(request);
        try {
        quartzJobMapper.updateById(quartzJob);
        } catch (Exception e) {
        return CommonResult.error(ErrorCodeEnum.CONTENT_REPEAT);
        }
        return CommonResult.success();
    }
    @Override
    public CommonResult page(QuartzJobPage request) {
    LambdaQueryWrapper<QuartzJob> qw = new LambdaQueryWrapper<QuartzJob>();
            if(ObjectUtils.isNotEmpty(request.getId())){
            qw.eq(QuartzJob::getId, request.getId());
            }
           if(StringUtils.isNotEmpty(request.getBeanName())){
           qw.eq(QuartzJob::getBeanName, request.getBeanName());
           }
           if(StringUtils.isNotEmpty(request.getCronExpression())){
           qw.eq(QuartzJob::getCronExpression, request.getCronExpression());
           }
           if(StringUtils.isNotEmpty(request.getJobName())){
           qw.eq(QuartzJob::getJobName, request.getJobName());
           }
           if(StringUtils.isNotEmpty(request.getMethodName())){
           qw.eq(QuartzJob::getMethodName, request.getMethodName());
           }
           if(StringUtils.isNotEmpty(request.getParams())){
           qw.eq(QuartzJob::getParams, request.getParams());
           }
           if(StringUtils.isNotEmpty(request.getRemark())){
           qw.eq(QuartzJob::getRemark, request.getRemark());
           }
           if(StringUtils.isNotEmpty(request.getCertType())){
           qw.eq(QuartzJob::getCertType, request.getCertType());
           }
           if(StringUtils.isNotEmpty(request.getTaskType())){
           qw.eq(QuartzJob::getTaskType, request.getTaskType());
           }
        Page<QuartzJob> quartzJobPage = quartzJobMapper.selectPage(request, qw);
         return CommonResult.success(quartzJobPage);
    }

    @Override
    public CommonResult updateByCronExpression(QuartzJobUpdateReq request) {
        QuartzJob quartzJob = QuartzJobConvert.INSTANCE.convert(request);
        try {
            quartzJobMapper.updateById(quartzJob);
            QuartzJob quartzJob1=quartzJobMapper.selectById(quartzJob.getId());
            System.out.println(JSONObject.toJSONString(quartzJob1));
            quartzManage.updateJobCron(quartzJob1);
        } catch (Exception e) {
            return CommonResult.error(ErrorCodeEnum.CONTENT_REPEAT);
        }
        return CommonResult.success();
    }

    @Override
    public CommonResult pauseAndResume(Long id, Boolean isUsing) {
        QuartzJob quartzJob=quartzJobMapper.selectById(id);
        quartzJob.setIsUsing(isUsing);
        try {
        quartzJobMapper.updateById(quartzJob);
        } catch (Exception e) {
            return CommonResult.error(ErrorCodeEnum.CONTENT_REPEAT);
        }
        if(isUsing){
            quartzManage.resumeJob(quartzJob);
        }else{
            quartzManage.pauseJob(quartzJob);
        }
        return CommonResult.success();
    }


    @Override
    public CommonResult list(QuartzJobSearchReq request) {
        List<QuartzJob> quartzJobs=quartzJobMapper.selectList(request);
        return CommonResult.success(quartzJobs);
        }
}
