package com.bot.patrol.info.dispatch.web;

import com.bot.patrol.info.dispatch.entity.CaseInfo;
import com.bot.patrol.info.dispatch.infrastructure.constants.CommonResult;
import com.bot.patrol.info.dispatch.model.req.GetRunCaseReq;
import com.bot.patrol.info.dispatch.model.req.QuartzJobAddReq;
import com.bot.patrol.info.dispatch.service.ICaseInfoService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequiredArgsConstructor
@RequestMapping("/agenter")
public class AgenterController {

    private final ICaseInfoService caseInfoService;

    @PostMapping(value = "/getRunCase")
    public CommonResult add(@RequestBody @Valid GetRunCaseReq request) {
        CaseInfo caseInfo = caseInfoService.selectBySid(request.getSid());
        return CommonResult.success(caseInfo);
    }
}
