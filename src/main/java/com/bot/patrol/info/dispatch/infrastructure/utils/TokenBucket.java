package com.bot.patrol.info.dispatch.infrastructure.utils;


import java.util.concurrent.TimeUnit;

public class TokenBucket {
    private final int rateLimit;    //限制每个时间间隔内的最大请求数
    private final long intervalInMillis;    //时间间隔的长度，以毫秒为单位。
    private long firstRequestTime;   //上次请求的时间戳。
    private int tokens;  //令牌桶中当前可用的[令牌数量]

    public TokenBucket(int rateLimit, long interval, TimeUnit timeUnit) {
        this.rateLimit = rateLimit;
        this.intervalInMillis = timeUnit.toMillis(interval); //将interval转换为毫秒
    }

    public synchronized boolean allowRequest() {
        //它获取当前的时间戳
        long currentTime = System.currentTimeMillis();
        //计算自上一次请求以来经过的时间。
        long timePassed = currentTime - firstRequestTime;

        if (timePassed >= intervalInMillis){
            //间隔时间超过一个周期，当前此次视为首次
            firstRequestTime = currentTime;
            tokens = rateLimit - 1;
        }else {
            if (tokens > 0) {
                //判断次数是否超过最大请求数
                tokens --;
            }else {
                //次数超过最大请求数，限制
                return false;
            }
        }
        return true;
    }


}
