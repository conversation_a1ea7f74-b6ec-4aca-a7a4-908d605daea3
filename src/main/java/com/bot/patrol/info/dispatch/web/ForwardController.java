package com.bot.patrol.info.dispatch.web;

import com.bot.patrol.info.dispatch.infrastructure.constants.AppConstant;
import com.bot.patrol.info.dispatch.infrastructure.utils.CommUtils;
import com.bot.patrol.info.dispatch.infrastructure.utils.OkHttpUtils;
import com.bot.patrol.info.dispatch.model.req.PostDataReq;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * 转发控制器：巡查信息管理
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/patrolInfoAdmin")
public class ForwardController {
    @Value("${proxy.targetAddr}")
    private String targetAddr;

    /**
     * 转发post请求，以json格式传参 到 数据中心
     * @param postDataReq
     * @param webToken
     * @param authToken
     * @return
     */
    @PostMapping(value = "/postJson")
    public String postJson(@RequestBody PostDataReq postDataReq, @RequestHeader(value = "Web-Token",required = false) String webToken,@RequestHeader(value = "Auth-Token",required = false) String authToken) {
        Map<String, String> headers=new HashMap<>();
        if(StringUtils.isNotEmpty(webToken)){
            headers.put(AppConstant.WEB_TOKEN,webToken);
        }
        if(StringUtils.isNotEmpty(authToken)){
            headers.put(AppConstant.AUTH_TOKEN,authToken);
        }
        return OkHttpUtils.httpPostJson(targetAddr+postDataReq.getUri(),headers , postDataReq.getPostJson());
    }

    /**
     * 转发post请求，以Form格式传参 到 数据中心
     * @param parm
     * @param url
     * @param file
     * @param webToken
     * @param authToken
     * @return
     * @throws IOException
     */
    @PostMapping(value = "/postForm")
    public String postForm(@RequestParam Map<String ,Object> parm, @RequestParam String url,@RequestParam MultipartFile[] file, @RequestHeader(value = "Web-Token",required = false) String webToken, @RequestHeader(value = "Auth-Token",required = false) String authToken) throws IOException {
        Map<String, String> headers=new HashMap<>();
        if(StringUtils.isNotEmpty(webToken)){
            headers.put(AppConstant.WEB_TOKEN,webToken);
        }
        if(StringUtils.isNotEmpty(authToken)){
            headers.put(AppConstant.AUTH_TOKEN,authToken);
        }
        String apkName = file[0].getOriginalFilename();
        String workPath = CommUtils.getWorkPath();
        String apkPath = workPath + AppConstant.APP_TEMP_PATH + apkName;
        System.out.println(apkPath);
        File apkFile=new File(apkPath);
        inputStreamToFile(file[0].getInputStream(),apkFile);
        parm.put("file",apkFile);
        parm.remove("url");
        String msg=null;
        try {
             msg=OkHttpUtils.httpMethod(targetAddr+url,parm , headers);
        }finally {
            delteTempFile(apkFile);
        }

        return msg;
    }
    /**
     * 删除本地临时文件
     * @param file
     */
    public static void delteTempFile(File file) {
        if (file != null) {
            File del = new File(file.toURI());
            del.delete();
        }
    }
    private static void inputStreamToFile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
