package com.bot.patrol.info.dispatch.infrastructure.pool;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.concurrent.*;

/**
 * 临时线程池
 */
@Slf4j
public class FixedThreadPool {

    /* 有界队列,超出队列数直接丢弃 */
    public static final ExecutorService async = new ThreadPoolExecutor(5, 20,
            60L, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>(100),
            Executors.defaultThreadFactory(), new ThreadPoolExecutor.AbortPolicy());

    public static void submit(String title, Run task) {
        log.info("start->{}",title);
        try {
            FixedThreadPool.async.submit(() -> {
                try {
                    task.start();
                } catch (Exception e) {
                    log.error(String.format("线程失败:%s->%s", title, e.getMessage()), e);
                } finally {
                }
            });
        } catch (Exception e) {
            log.error(String.format("线程失败->"+title+":%s->%s", title, e.getMessage()), e);
        }
    }

    public static interface Run {
        void start() throws IOException;
    }
}
