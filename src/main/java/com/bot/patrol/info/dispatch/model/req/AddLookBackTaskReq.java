package com.bot.patrol.info.dispatch.model.req;

import com.bot.patrol.info.dispatch.entity.ext.LookBackItem;
import com.bot.patrol.info.dispatch.entity.ext.LookBackOrder;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

@ApiModel(value="回查任务新增",description="回查任务新增")
@Data
public class AddLookBackTaskReq {
    private Integer tiType;
    private String tiExternalId;
    private String tiExternalCallBackUrl;
    //应用类型 300300网页 300308微博 300303公众号
    private List<LookBackOrder> orderLists;
    private List<LookBackItem>  docList;
}