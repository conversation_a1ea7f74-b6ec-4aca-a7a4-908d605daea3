package com.bot.patrol.info.dispatch.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bot.patrol.info.dispatch.entity.LookBackUrlList;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface LookBackUrlListMapper extends BaseMapper<LookBackUrlList> {
    default List<LookBackUrlList> selectByTaskSid(String taskSid){
        return selectList(new LambdaQueryWrapper<LookBackUrlList>().eq(LookBackUrlList::getLulLbtSid, taskSid));
    }
    default boolean getFinishStateByTaskSid(String taskSid){
        return selectCount(new LambdaQueryWrapper<LookBackUrlList>().eq(LookBackUrlList::getLulLbtSid, taskSid).eq(LookBackUrlList::getLulState, 0)) == 0;
    }
    default int updateEntity(LookBackUrlList lookBackUrlList){
        return updateById(lookBackUrlList);
    }

    default List<LookBackUrlList> selectByTaskSidAndUrl(String taskSid, String url){
        return selectList(new LambdaQueryWrapper<LookBackUrlList>()
                .eq(LookBackUrlList::getLulLbtSid, taskSid)
                .eq(LookBackUrlList::getLulUrl,url));
    }
}
