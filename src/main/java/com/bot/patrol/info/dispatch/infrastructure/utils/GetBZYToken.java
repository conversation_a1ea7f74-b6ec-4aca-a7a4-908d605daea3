package com.bot.patrol.info.dispatch.infrastructure.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bot.patrol.info.dispatch.model.BZYToken;
import okhttp3.*;
import org.apache.commons.lang3.ObjectUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class GetBZYToken {
    private  RedisUtils redisUtils;
    public GetBZYToken(RedisUtils redisUtils){
        this.redisUtils =redisUtils;
    }
    public  Map<String,String> getToken() throws IOException {
        Map<String,String> map=new HashMap<>();
        BZYToken bzyToken=redisUtils.get("bzyToekn");
        if(ObjectUtils.isEmpty(bzyToken)){
            String s=OkHttpUtils.postDataByForm("https://advancedapi.bazhuayu.com/token", "username=13716328969&password=1q2w3e4r&grant_type=password", null);
            bzyToken= JSONObject.parseObject(s,BZYToken.class);
            redisUtils.set("bzyToekn", bzyToken, Integer.parseInt(bzyToken.getExpiresIn()), TimeUnit.SECONDS);
        }
        map.put("Authorization", bzyToken.getTokenType()+" "+bzyToken.getAccessToken());
        return map;
    }
}
