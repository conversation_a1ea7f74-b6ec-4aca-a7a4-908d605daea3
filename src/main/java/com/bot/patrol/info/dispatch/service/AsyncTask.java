package com.bot.patrol.info.dispatch.service;

import com.alibaba.fastjson.JSONObject;
import com.bot.patrol.info.dispatch.infrastructure.utils.OkHttpUtils;
import com.bot.patrol.info.dispatch.model.IdUrlModel;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Future;

@Component
public class AsyncTask {
    @Value("${convertPdfUrl}")
    private String convertPdfUrl;
    @Async
    public Future<String> execTaskA(List<IdUrlModel> idUrlModels) throws InterruptedException {
        System.out.println("TaskA开始");
        try {
            String postJson = OkHttpUtils.httpPostJson(convertPdfUrl, JSONObject.toJSONString(idUrlModels));
            System.out.println("请求html转换返回数据:" + postJson);
        }catch (Exception e){
            e.printStackTrace();
        }
        return new AsyncResult<>("TaskA结束");
    }
}
