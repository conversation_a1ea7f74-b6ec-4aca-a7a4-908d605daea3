package com.bot.patrol.info.dispatch.infrastructure.config;

import lombok.RequiredArgsConstructor;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
public class RedissonManager {
    private static RedissonManager redissonManager;
    @Value("${spring.redis.host}")
    private String REDIS_HOST;
    @Value("${spring.redis.port}")
    private String REDIS_PORT;
    @Value("${spring.redis.password}")
    private String REDIS_PASSWORD;

    private static Config config = new Config();
    //声明redisso对象
    private static Redisson redisson = null;
    //实例化redisson

    @PostConstruct
    public void init(){
        redissonManager = this;
        if(Objects.isNull(redissonManager.redisson)) {
            redissonManager.config.useSingleServer().setAddress("redis://" + REDIS_HOST + ":" + REDIS_PORT);
            redissonManager.config.useSingleServer().setDatabase(0);
            redissonManager.config.useSingleServer().setPassword(REDIS_PASSWORD);
            //得到redisson对象
            redissonManager.redisson = (Redisson) Redisson.create(redissonManager.config);
        }
    }

    //获取redisson对象的方法
    public static Redisson getRedisson(){
        return redisson;
    }

    public static boolean addLock(String lockName){
        Redisson redisson = RedissonManager.getRedisson();
        String key = lockName;
        //获取锁对象
        RLock mylock = redisson.getLock(key);
        //加锁，并且设置锁过期时间，防止死锁的产生
        mylock.lock(10, TimeUnit.MINUTES);
        //加锁成功
        return  true;
    }

    public static boolean isLock(String lockName){
        return redissonManager.redisson.getLock(lockName).isLocked();
    }

    public static void releaseLock(String lockName){
        //必须是和加锁时的同一个key
        String key = lockName;
        //获取所对象
        RLock mylock = redisson.getLock(key);
        //释放锁（解锁）
        mylock.unlock();
    }
}
