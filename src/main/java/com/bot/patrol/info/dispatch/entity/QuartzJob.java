package com.bot.patrol.info.dispatch.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
* <AUTHOR>
* @date 2022-03-03
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("quartz_job")
public class QuartzJob implements Serializable {

    public static final String  JOB_KEY = "JOB_KEY";
    // ID
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    // Spring Bean名称
    @TableField(value = "bean_name")
    private String beanName;

    // cron 表达式
    @TableField(value = "cron_expression")
    private String cronExpression;

    // 状态：1暂停、0启用
    @TableField(value = "is_using")
    private Boolean isUsing;

    // 任务名称
    @TableField(value = "job_name")
    private String jobName;

    // 方法名称
    @TableField(value = "method_name")
    private String methodName;

    // 参数
    @TableField(value = "params")
    private String params;

    // 备注
    @TableField(value = "remark")
    private String remark;

    // 任务开始时间
    @TableField(value = "start_time")
    private LocalDateTime startTime;

    // 任务开始时间
    @TableField(value = "end_time")
    private LocalDateTime endTime;

    // 创建或更新日期
    @TableField(value = "update_time", insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime updateTime;

    @TableField(value = "cert_type")
    private String certType;

    @TableField(value = "task_type")
    private Integer taskType;

    @TableField(value = "cycle_type")
    private Integer cycleType;

    @TableField(value = "cycle_num")
    private Integer cycleNum;

    @TableField(value = "is_push")
    private Boolean isPush;

}
