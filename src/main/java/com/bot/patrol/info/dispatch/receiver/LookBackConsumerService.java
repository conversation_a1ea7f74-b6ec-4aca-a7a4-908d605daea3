package com.bot.patrol.info.dispatch.receiver;


import com.bot.patrol.info.dispatch.entity.LookBackTask;
import com.bot.patrol.info.dispatch.entity.LookBackUrlList;
import com.bot.patrol.info.dispatch.infrastructure.utils.OkHttpUtils;
import com.bot.patrol.info.dispatch.infrastructure.utils.TokenBucketUtils;
import com.bot.patrol.info.dispatch.model.ESModel;
import com.bot.patrol.info.dispatch.model.ESModelDetail;
import com.bot.patrol.info.dispatch.model.dto.PushDateDTO;
import com.bot.patrol.info.dispatch.service.ILookBackTaskService;
import com.bot.patrol.info.dispatch.service.ILookBackUrlListService;
import com.google.gson.JsonNull;
import com.rabbitmq.client.Channel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONNull;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;

@Slf4j
@Component
public class LookBackConsumerService {
    private final ILookBackTaskService lookBackTaskService;
    private final ILookBackUrlListService lookBackUrlListService;


    private String internalSystemAddress;
    @Autowired
    private TokenBucketUtils tokenBucketUtils;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Value("${customs.props.customer-direct-fail-callback}")
    private String directFcb;
    @Value("${customs.props.customer-key-fail-callback}")
    private String keyFcb;

    public LookBackConsumerService(ILookBackTaskService lookBackTaskService, ILookBackUrlListService lookBackUrlListService, @Value("${internalSystemAddress}") String internalSystemAddress){
        this.lookBackTaskService = lookBackTaskService;
        this.lookBackUrlListService = lookBackUrlListService;
        this.internalSystemAddress = internalSystemAddress;
    }
    @RabbitListener(
            bindings=@QueueBinding(
                    value=@Queue(value="${customs.task.rabbitmq-queue-lookback-result-webo}",autoDelete="false"),
                    exchange=@Exchange(value="${customs.task.rabbitmq-direct-lookback-result}",durable="true", type= ExchangeTypes.DIRECT),
                    key="${customs.task.rabbitmq-key-lookback-webo}"
            ), concurrency = "5", ackMode = "MANUAL"
    )
    @RabbitHandler
    public void weboResult(Message message, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        log.info("收到微博回查结果消息:{}", message);
        try {
            String msg = new String(message.getBody(), StandardCharsets.UTF_8);
            JSONObject joMsg = JSONObject.fromObject(msg);
            sendResult(joMsg);
            channel.basicAck(deliveryTag, false);
            log.info("微博回查结果消息处理完成:{}", message);
        }catch (Exception e){
            if (e.getMessage().equals("没有对应的任务信息")){
                log.error("处理微信回查结果消息失败:{}", message, e);
                channel.basicAck(deliveryTag, false);
            }else{
                log.error("处理微信回查结果消息失败:{}", message, e);
                channel.basicNack(deliveryTag, false, true);
            }
        }

    }


    @RabbitListener(
            bindings=@QueueBinding(
                    value=@Queue(value="${customs.task.rabbitmq-queue-lookback-result-wechat}",autoDelete="false"),
                    exchange=@Exchange(value="${customs.task.rabbitmq-direct-lookback-result}",durable="true", type= ExchangeTypes.DIRECT),
                    key="${customs.task.rabbitmq-key-lookback-wechat}"
            ), concurrency = "5", ackMode = "MANUAL"
    )
    @RabbitHandler
    public void wechatResult(Message message, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        log.info("收到微信回查结果消息:{}", message);
        try {
            String msg = new String(message.getBody(), StandardCharsets.UTF_8);
            JSONObject joMsg = JSONObject.fromObject(msg);
            sendResult(joMsg);
            channel.basicAck(deliveryTag, false);
            log.info("微信回查结果消息处理完成:{}", message);
        }catch (Exception e){
            if (e.getMessage().equals("没有对应的任务信息")){
                log.error("处理微信回查结果消息失败:{}", message, e);
                channel.basicAck(deliveryTag, false);
            }else{
                log.error("处理微信回查结果消息失败:{}", message, e);
                channel.basicNack(deliveryTag, false, true);
            }
        }

    }

    private void sendResult(JSONObject joMsg) throws Exception {
        LookBackTask lookBackTask = lookBackTaskService.selectBySid(joMsg.getString("taskSid"));
        LookBackUrlList lookBackUrlList = lookBackUrlListService.selectBySid(joMsg.getString("itemSid"));
        if(Objects.nonNull(lookBackTask) && Objects.nonNull(lookBackUrlList)) {
            ESModel jsonObject = new ESModel();
            jsonObject.setId(lookBackTask.getSid());
            jsonObject.setProjectId(null);
            jsonObject.setDataId(lookBackUrlList.getLulExternalId());
            jsonObject.setTitle(joMsg.getString("title"));
            jsonObject.setAppId(null);
            jsonObject.setTaskId(lookBackTask.getLbtExternalId());
            jsonObject.setCaseId(null);
            jsonObject.setHitUrl(lookBackUrlList.getLulUrl());
            if (joMsg.get("publishTime").equals(JSONNull.getInstance()) || joMsg.getString("publishTime").isEmpty()) {
                jsonObject.setScreenTime(null);
            } else {
                LocalDateTime localStartDateTime = LocalDateTime.parse(joMsg.getString("publishTime"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                jsonObject.setScreenTime(localStartDateTime);
            }

            jsonObject.setEventId(null);
            jsonObject.setSearchWordId(null);
            jsonObject.setModuleName("");
            jsonObject.setTypeCode(lookBackTask.getLbtType());
            jsonObject.setReadTheNumber("0");
            jsonObject.setThumbUpFor("0");
            jsonObject.setLookingAtTheNumber("0");
            jsonObject.setForwardingNumber("0");
            jsonObject.setComments("0");
            List<ESModelDetail> esModelDetails = new ArrayList<>();
            List<String> im = new ArrayList<>();
            String originalPictures = joMsg.getString("imgs");
            if (StringUtils.isNotEmpty(originalPictures)) {
                String[] imgUrl = originalPictures.split(",");
                im.addAll(Arrays.asList(imgUrl));
            }
            jsonObject.setText(joMsg.getString("content"));
            jsonObject.setEsModelDetailList(esModelDetails);
            jsonObject.setImgList(im);
            jsonObject.setCallBackUrl(null);
            if (Objects.nonNull(jsonObject.getScreenTime())) {
                DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime ldt = LocalDateTime.parse(jsonObject.getScreenTime().format(df), df);
                jsonObject.setScreenTime(ldt);
            } else {
                jsonObject.setScreenTime(null);
            }
//            jsonObject.setAttachmentList(new ArrayList<>());
//            List<String> videoList = new ArrayList<>();
//            String videoUrls = joMsg.containsKey("videos") ? joMsg.getString("videos") : null;
//            if (StringUtils.isNotEmpty(videoUrls)){
//                if(videoUrls.contains(",video")){
//                    String regex = "http.*?,video";
//                    Pattern pattern = Pattern.compile(regex);
//                    Matcher matcher = pattern.matcher(videoUrls);
//                    while (matcher.find()) {
//                        // 获取匹配到的子字符串
//                        String match = matcher.group();
//                        videoList.add(match);
//                    }
//                }else{
//                    String[] videoUrl = videoUrls.split(",");
//                    videoList.addAll(Arrays.asList(videoUrl));
//                }
//            }
//            jsonObject.setVideoList(videoList);
            log.info(com.alibaba.fastjson.JSONObject.toJSONString(jsonObject));

            boolean defaultUrl = false;
            String url = lookBackTask.getLbtCallbackUrl();
            String content = com.alibaba.fastjson.JSONObject.toJSONString(jsonObject);
            String datas = null;
            if (StringUtils.isNotEmpty(url)) {
                //调用回调pushData：mq消费
                //String datas = OkHttpUtils.httpPostJson(lookBackTask.getLbtCallbackUrl(), com.alibaba.fastjson.JSONObject.toJSONString(jsonObject));
                datas = tokenBucketUtils.throttlingCallBack(url, content);

                //log.info("推送数据(" +joMsg.getString("dataType") + "):" + datas);
            } else {
                defaultUrl = true;
                url = internalSystemAddress + "api/third/patrolAudit";
                datas = OkHttpUtils.httpPostJson(url, content);
                //log.info("推送数据(" +joMsg.getString("dataType") + "):" + datas);
            }
            if (StringUtils.isEmpty(datas) || (!datas.contains("\"code\":200") && !datas.contains("\"code\":500200") &&!datas.contains("\"code\":500203"))) {
                PushDateDTO pushDateDTO = new PushDateDTO();
                pushDateDTO.setUrl(url);
                pushDateDTO.setContent(content);
                pushDateDTO.setDefaultUrl(defaultUrl);
                lookBackUrlList.setLulState(1);
                pushDateDTO.setLookBackUrlList(lookBackUrlList);
                if(lookBackUrlListService.getFinishStateByTaskSid(lookBackTask.getSid())){
                    lookBackTask.setLbtState(1);
                    pushDateDTO.setLookBackTask(lookBackTask);
                }
                rabbitTemplate.convertAndSend(directFcb, keyFcb, com.alibaba.fastjson.JSONObject.toJSONString(pushDateDTO));
                log.warn("推送回查数据失败：{}" , datas);
            }else {
                lookBackUrlList.setLulState(1);
                lookBackUrlListService.updateEntity(lookBackUrlList);
                if (lookBackUrlListService.getFinishStateByTaskSid(lookBackTask.getSid())) {
                    lookBackTask.setLbtState(1);
                    lookBackTaskService.updateEntity(lookBackTask);
                }
                log.info("推送回查数据成功：{}" , content);
            }
        }else{
            throw new Exception("没有对应的任务信息");
        }
    }
}
