package com.bot.patrol.info.dispatch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("task_info")
public class TaskInfo {
    /**
     * sid
     */
    @TableId(value = "sid")
    private String sid;

    /**
     * ti_pi_sid
     * 项目id
     */
    @TableField(value = "ti_pi_sid")
    private String tiPiSid;

    /**
     * ti_cpi_sid
     * 用户id
     */
    @TableField(value = "ti_cpi_sid")
    private String tiCpiSid;

    /**
     * ti_cpi_name
     * 用户名称
     */
    @TableField(value = "ti_cpi_name")
    private String tiCpiName;

    /**
     * ti_type
     * 任务类型
     */
    @TableField(value = "ti_type")
    private Integer tiType;

    /**
     * ti_sno
     * 任务编号
     */
    @TableField(value = "ti_sno")
    private String tiSno;

    /**
     * ti_descr
     * 任务描述
     */
    @TableField(value = "ti_descr")
    private String tiDescr;

    /**
     * ti_industry_type
     * 所属行业类型
     */
    @TableField(value = "ti_industry_type")
    private String tiIndustryType;

    /**
     * ti_run_type
     * 运行类型
     */
    @TableField(value = "ti_run_type")
    private Integer tiRunType;

    /**
     * ti_run_cycle
     * 周期类型
     */
    @TableField(value = "ti_run_cycle")
    private Integer tiRunCycle;

    /**
     * ti_cycle_num
     * 周期类型值
     */
    @TableField(value = "ti_cycle_num")
    private Integer tiCycleNum;

    /**
     * ti_start_time
     * 计划开始时间
     */
    @TableField(value = "ti_start_time")
    private LocalDateTime tiStartTime;

    /**
     * ti_end_time
     * 计划结束时间
     */
    @TableField(value = "ti_end_time")
    private LocalDateTime tiEndTime;

    /**
     * ti_min_actuator
     * 最小执行器占用数
     */
    @TableField(value = "ti_min_actuator")
    private Integer tiMinActuator;

    /**
     * ti_max_actuator
     * 最大执行器占用数
     */
    @TableField(value = "ti_max_actuator")
    private Integer tiMaxActuator;

    /**
     * ti_priority
     * 任务优先级
     */
    @TableField(value = "ti_priority")
    private Integer tiPriority;

    /**
     * ti_run_num
     * 任务运行次数
     */
    @TableField(value = "ti_run_num")
    private Integer tiRunNum;

    /**
     * ti_status
     * 任务运行状态
     */
    @TableField(value = "ti_status")
    private Integer tiStatus;

    /**
     * ti_state
     * 任务启用状态
     */
    @TableField(value = "ti_state")
    private Integer tiState;

    /**
     * ti_create_time
     * 任务创建时间
     */
    @TableField(value = "ti_create_time")
    private LocalDateTime tiCreateTime;

    /**
     * ti_update_time
     * 任务更新时间
     */
    @TableField(value = "ti_update_time")
    private LocalDateTime tiUpdateTime;


    @TableField(exist = false)
    private String tiExternalId;

    @TableField(exist = false)
    private String tiCrawlerId;

    @TableField(exist = false)
    private Integer tiTypeChild;

    /**
     * 外部回调url
     */
    @TableField(exist = false)
    private String tiExternalCallBackUrl;



}
