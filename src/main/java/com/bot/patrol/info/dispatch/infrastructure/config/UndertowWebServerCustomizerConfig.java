package com.bot.patrol.info.dispatch.infrastructure.config;

import io.undertow.server.handlers.DisallowedMethodsHandler;
import io.undertow.util.HttpString;
import org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Configuration;

@Configuration
public class UndertowWebServerCustomizerConfig implements WebServerFactoryCustomizer<UndertowServletWebServerFactory> {

    @Override
    public void customize(UndertowServletWebServerFactory factory) {
        factory.addDeploymentInfoCustomizers(
                deploymentInfo -> deploymentInfo.addInitialHandlerChainWrapper(
                        handler -> {
                            HttpString[] disallowedHttpMethods = {HttpString.tryFromString("TRACE"),
                                    HttpString.tryFromString("TRACK")};
                            return new DisallowedMethodsHandler(handler, disallowedHttpMethods);
                        }));
    }
}