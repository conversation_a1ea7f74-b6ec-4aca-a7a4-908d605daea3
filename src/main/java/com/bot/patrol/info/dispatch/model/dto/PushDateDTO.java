package com.bot.patrol.info.dispatch.model.dto;

import com.bot.patrol.info.dispatch.entity.LookBackTask;
import com.bot.patrol.info.dispatch.entity.LookBackUrlList;
import com.bot.patrol.info.dispatch.model.IdUrlModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 推送数据对象
 */
@Data
public class PushDateDTO implements Serializable {

    private static final long serialVersionUID = 831880253351053016L;

    private String url;

    private String content;

    private Boolean defaultUrl = false;

    private LookBackUrlList lookBackUrlList;

    private LookBackTask lookBackTask;

    private List<IdUrlModel> idUrlModels;

}
