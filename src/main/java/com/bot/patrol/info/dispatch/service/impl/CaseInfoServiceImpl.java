package com.bot.patrol.info.dispatch.service.impl;

import com.bot.patrol.info.dispatch.entity.CaseInfo;
import com.bot.patrol.info.dispatch.mapper.CaseInfoMapper;
import com.bot.patrol.info.dispatch.model.req.CaseInfoUpdateReq;
import com.bot.patrol.info.dispatch.service.ICaseInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class CaseInfoServiceImpl implements ICaseInfoService {
    private final CaseInfoMapper caseInfoMapper;

    @Override
    public int add(CaseInfo caseInfo) {
        return caseInfoMapper.add(caseInfo);
    }

    @Override
    public int update(CaseInfo caseInfo) {
        return caseInfoMapper.update(caseInfo);
    }

    @Override
    public CaseInfo selectBySid(String sid) {
        return caseInfoMapper.selectBySid(sid);
    }

    @Override
    public void deleteByTaskId(String taskId) {
        caseInfoMapper.deleteByTaskId(taskId);
    }

    @Override
    public List<CaseInfo> selectByTaskSid(String taskId) {
        return caseInfoMapper.selectByTaskSid(taskId);
    }
}
