package com.bot.patrol.info.dispatch.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
* <AUTHOR>
* @date 2022-02-08
*/
@ApiModel(value="任务信息对象查询",description="任务信息")
@Data
public class TaskInfoSearchReq {
        private String sid;
        // 项目id
        @ApiModelProperty(value="项目id",name="项目id")
        private String tiPiSid;
        // 用户id
        @ApiModelProperty(value="用户id",name="用户id")
        private String tiCpiSid;
        // 任务用户实体名称
        @ApiModelProperty(value="任务用户实体名称",name="任务用户实体名称")
        private String tiCpiName;
        // 任务类型
        @ApiModelProperty(value="任务类型",name="任务类型")
        private Integer tiType;
        // 任务标识
        @ApiModelProperty(value="任务标识",name="任务标识")
        private String tiSno;
        // 任务描述
        @ApiModelProperty(value="任务描述",name="任务描述")
        private String tiDescr;
        // 归属行业
        @ApiModelProperty(value="归属行业",name="归属行业")
        private String tiIndustryType;
        // 任务运行类型
        @ApiModelProperty(value="任务运行类型",name="任务运行类型")
        private Integer tiRunType;
        // 任务周期运行类型
        @ApiModelProperty(value="任务周期运行类型",name="任务周期运行类型")
        private Integer tiRunCycle;
        // 任务周期运行值
        @ApiModelProperty(value="任务周期运行值",name="任务周期运行值")
        private Integer tiCycleNum;
        // 任务计划开始时间
        @ApiModelProperty(value="任务计划开始时间",name="任务计划开始时间")
        private LocalDateTime tiStartTime;
        // 任务计划结束时间
        @ApiModelProperty(value="任务计划结束时间",name="任务计划结束时间")
        private LocalDateTime tiEndTime;
        // 最小占用执行器数量
        @ApiModelProperty(value="最小占用执行器数量",name="最小占用执行器数量")
        private Integer tiMinActuator;
        // 最大占用执行器数量
        @ApiModelProperty(value="最大占用执行器数量",name="最大占用执行器数量")
        private Integer tiMaxActuator;
        // 执行优先级
        @ApiModelProperty(value="执行优先级",name="执行优先级")
        private Integer tiPriority;
        // 运行次数
        @ApiModelProperty(value="运行次数",name="运行次数")
        private Integer tiRunNum;
        // 任务运行状态
        @ApiModelProperty(value="任务运行状态",name="任务运行状态")
        private Integer tiStatus;
        // 是否停用 0-否 1-是
        @ApiModelProperty(value="是否停用 0-否 1-是",name="是否停用 0-否 1-是")
        private Integer tiState;

        @ApiModelProperty(value="是否删除0删除1未删除",name="是否删除0删除1未删除")
        private Integer tiDelete;

        @ApiModelProperty(value="外部id",name="外部id")
        private String tiExternalId;

        @ApiModelProperty(value="爬虫引擎id",name="爬虫引擎id")
        private String tiCrawlerId;

        @ApiModelProperty(value="业务类型集合",name="业务类型集合")
        private List<Integer> typeList;

        @ApiModelProperty(value="应用类型",name="应用类型")
        private Integer appType;


        private String appUrl;


}
