package com.bot.patrol.info.dispatch.infrastructure.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class TokenBucketUtils {
    public Map<String, TokenBucket> tokenBucketMap = new HashMap<>();

    public String throttlingCallBack(String url, String json) {
        TokenBucket tokenBucket = null;
        if (!tokenBucketMap.containsKey(url)) {
            tokenBucket = new TokenBucket(600, 1, TimeUnit.MINUTES); // 每秒最多处理10个请求
            tokenBucketMap.put(url, tokenBucket);
        }else {
            tokenBucket = tokenBucketMap.get(url);
        }
        if (tokenBucket.allowRequest()){
            try {
                //return "aaa\"code\":200hhaa";
                return OkHttpUtils.httpPostJson(url.replace("http://10.0.0.77:9077", "https://pgc.botsmart.cn/api"), json);
            }catch (Exception e){
                log.info("请求异常：{}", e.getMessage());
                e.printStackTrace();
            }
        }else {
            return "令牌已用光";
        }
        return null;
    }
}
