package com.bot.patrol.info.dispatch.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
* <AUTHOR>
* @date 2022-02-08
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderList implements Serializable {

    // id
    private String sid;

    // 订单类型
    private Integer olType;

    // 订单来源
    private Integer olOrigin;

    // 客服方id
    private String olCpiSid;

    // 应用id
    private String olAiSid;

    // 图标
    private String olAiIcon;

    // 编号
    private String olAiSno;

    // 名称
    private String olAiName;

    // 包名
    private String olAiPackage;

    // 版本
    private String olAiVersion;

    // 应用地址
    private String olAiUrl;

    // 建议巡查方式
    private Integer olPatrolMode;

    // 巡查目标
    private String olAim;

    // 易违规点
    private String olViolationPoint;

    // 建议巡查步骤
    private String olPatrolStep;

    // 附件地址
    private String olEnclosureUrl;

    // 应用类型0-已分析1-不可用
    private Integer olAppType;

    // 备注
    private String olRemark;

    // 操作人id
    private String olOperatorSid;

    // 应用类型
    private Integer olAiSysType;

    // 创建时间
    private LocalDateTime olCreateTime;

    // 更新时间
    private LocalDateTime olUpdateTime;

    private String appAnalyseInfoSid;

    private String auditNum;

    // 应用地址
    private String olSno;

    // 应用地址
    private String olAiGetOrigin;


}
