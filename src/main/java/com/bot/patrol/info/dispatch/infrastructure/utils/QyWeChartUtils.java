package com.bot.patrol.info.dispatch.infrastructure.utils;

import com.alibaba.fastjson.JSONObject;

public class QyWeChartUtils {
    private  static  String url="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4b6e42ea-64e3-4377-86cc-78d2773b8547";

    public static void sendMsgByText(String content){
        JSONObject jsonObject=new JSONObject();
        jsonObject.put("msgtype", "text");
        JSONObject text=new JSONObject();
        text.put("content", content);
        jsonObject.put("text", text);
        OkHttpUtils.httpPostJson(url, jsonObject.toJSONString());

    }

    public static void main(String[] args) {
        sendMsgByText("脚本运行异常，脚本编号为CS-164682230102665");
    }
}
