package com.bot.patrol.info.dispatch.infrastructure.utils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.time.LocalDateTime;
import java.time.chrono.Chronology;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Configuration
@EnableScheduling
@EnableAsync
@Async
@RequiredArgsConstructor
public class CommUtils {

    private final static DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final static DateTimeFormatter dfDate = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    private final static DateTimeFormatter dfToday = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final static String[] chars = new String[] { "a", "b", "c", "d", "e", "f",
            "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s",
            "t", "u", "v", "w", "x", "y", "z", "0", "1", "2", "3", "4", "5",
            "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "I",
            "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V",
            "W", "X", "Y", "Z" };
    private static CommUtils commUtils;

    @Value("${app.baseUrl}")
    private String patrolDataCenterUrl;
    @Value("${customs.appKey}")
    private String appKey;
    @Value("${customs.appSecret}")
    private String appSecret;

    @Resource
    private RedisUtils redisUtilsTemp;
    private static RedisUtils redisUtils;

    @PostConstruct
    public void init() {
        commUtils = this;
        redisUtils = redisUtilsTemp;
    }

    public static String convertDateTime(LocalDateTime d){
        return df.format(d);
    }

    public static String convertDateMs(LocalDateTime d){
        return dfDate.format(d);
    }

    public static String convertToday(LocalDateTime d){
        return dfToday.format(d);
    }

    public static LocalDateTime ConvertByMapValue(Map<String,Object> map){
        try {
            return LocalDateTime.of(
                    (Integer) map.get("year"),
                    (Integer) map.get("monthValue"),
                    (Integer) map.get("dayOfMonth"),
                    (Integer) map.get("hour"),
                    (Integer) map.get("minute"),
                    (Integer) map.get("second"));
        }catch (Exception ex){
            return null;
        }
    }

    public static String getWorkPath(){
        String workPath = System.getProperty("user.dir");
        if (workPath.contains("\\")) {
            workPath = workPath.replace("\\", "/");
        }
        return workPath;
    }

    public static String getCenterUrl(){
        return commUtils.patrolDataCenterUrl;
    }

    public static Map<String, String> getCenterTokenHeader(){
        String patrolDataCenterTokenUrl = commUtils.patrolDataCenterUrl + "auth/token/get";
        Map<String, String> headers = new HashMap<>();
        headers.put("content-type","application/json;charset=UTF-8");
        Map<String, String> data = new HashMap<>();
        data.put("appKey", commUtils.appKey);
        data.put("appSecret",commUtils.appSecret);
        String strTokenData = OkHttpUtils.httpPostJson(patrolDataCenterTokenUrl, headers, JsonUtils.objectToJson(data));
        if(Objects.nonNull(strTokenData)){
            Map<String, Object> map = JsonUtils.jsonToMap(strTokenData);
            if((Integer)map.get("code") == 200){
                headers.put("Auth-Token", (String)map.get("data"));
                return headers;
            }
        }
        return null;
    }


    public static String getUUID32(){
        return UUID.randomUUID().toString().replace("-", "").toLowerCase();
    }

    /**
     * 获取用户真实IP地址，不使用request.getRemoteAddr()的原因是有可能用户使用了代理软件方式避免真实IP地址,
     * 可是，如果通过了多级反向代理的话，X-Forwarded-For的值并不止一个，而是一串IP值
     */
    public static String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_CLIENT_IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_X_FORWARDED_FOR");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
                if (ip.equals("0:0:0:0:0:0:0:1") || ip.equals("127.0.0.1")){
                    ip = getIpAddress();
                }
            }
        } else if (ip.length() > 15) {
            String[] ips = ip.split(",");
            for (int index = 0; index < ips.length; index++) {
                String strIp = (String) ips[index];
                if (!("unknown".equalsIgnoreCase(strIp))) {
                    ip = strIp;
                    break;
                }
            }
        }
        return ip;
    }

    public static String getIpAddress() {
        try {
            Enumeration<NetworkInterface> allNetInterfaces = NetworkInterface.getNetworkInterfaces();
            InetAddress ip = null;
            while (allNetInterfaces.hasMoreElements()) {
                NetworkInterface netInterface = (NetworkInterface) allNetInterfaces.nextElement();
                if (netInterface.isLoopback() || netInterface.isVirtual() || !netInterface.isUp()) {
                    continue;
                } else {
                    Enumeration<InetAddress> addresses = netInterface.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        ip = addresses.nextElement();
                        if (ip != null && ip instanceof Inet4Address) {
                            return ip.getHostAddress();
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("IP地址获取失败:{}", e);
        }
        return "";
    }

    public static Message ConvetMQJsonMsg(JSONObject jo){
        return MessageBuilder
                .withBody(jo.toString().getBytes())
                .setContentType(MessageProperties.CONTENT_TYPE_JSON)
                .build();
    }

    private String getTokenBaseKey(){ return MD5(commUtils.convertToday(LocalDateTime.now()), 16); }

    public static String MD5(String sourceStr,int bit) {
        String ret = DigestUtils.md5DigestAsHex(BASE64(sourceStr,"encod").getBytes());
        String result = "";
        for (char c :ret.toUpperCase().toCharArray()){
            int chaASCII = c;
            if (chaASCII >= 48 && chaASCII <= 53){
                result = result + (char)(chaASCII + 17);
            }else if (chaASCII >= 65 && chaASCII <= 70){
                result = result + (char)(chaASCII - 17);
            }else{
                result = result + c;
            }
        }
        if(bit <=0 || bit > 32){
            bit = 32;
        }
        int sIndex = 16 - bit/2 - bit%2;
        int eIndex = sIndex + bit;
        return result.substring(sIndex, eIndex).toLowerCase();
    }

    public static String ShortUUID(){
        StringBuffer shortBuffer = new StringBuffer();
        String uuid = UUID.randomUUID().toString();
        uuid=uuid.replace("-", "");
//        System.out.println(uuid);
        for (int i = 0; i < 8; i++) {
            String str = uuid.substring(i * 4, i * 4 + 4);
            int x = Integer.parseInt(str, 16);
            shortBuffer.append(chars[x % 62]);
        }
        return shortBuffer.toString();
    }


    //自定义加密
    private static final String myEncode(String str,int position) {
        String temp = str.toUpperCase();
        char chrArray[] = new char[temp.length()];
        for (int i=0; i < temp.length(); i++) {
            int chrASC = temp.charAt(i);
            if(chrASC >= 48 || chrASC <=57){
                chrArray[i] = (char) ((chrASC - 16) + position);
            }else{
                chrArray[i] = (char) ((chrASC) + position);
            }
        }
        return String.valueOf(chrArray).toLowerCase();
    }
    //自定义解密
    private static final String myDecode(String str,int position) {
        String temp = str.toUpperCase();
        char chrArray[] = new char[temp.length()];
        for (int i=0; i < temp.length(); i++) {
            int chrASC = temp.charAt(i);
            int flag = (chrASC - position) + 16;
            if(flag >= 48 || chrASC <=57){
                chrArray[i] = (char) flag;
            }else {
                chrArray[i] = (char) ((chrASC) - position);
            }
        }
        return String.valueOf(chrArray).toLowerCase();
    }

    public static String BASE64(String str,String type) {
        if(type.equals("encod")) {
            //Base64 加密
            byte[] bytes = str.getBytes();
            for (int i = 0; i < bytes.length; i++) {
                bytes[i] = (byte)(bytes[i] ^ 2021);
            }
            return Base64.getEncoder().encodeToString(bytes);
        }else if (type.equals("decod")) {
            //Base64 解密
            byte[] decoded = Base64.getDecoder().decode(str);
            for (int i = 0; i < decoded.length; i++) {
                decoded[i] = (byte)((decoded[i] ^ 2021));
            }
            return new String(decoded);
        }else{
            log.error("code:503201,msg:[输入参数错误]type类型输入错误");
            return null;
        }
    }

    public static boolean convertBool(Object obj,boolean DefaultValue){
        if(obj == null || obj.toString().isEmpty()){
            return DefaultValue;
        }else if(obj.toString().equals("true")){
            return true;
        }else if(obj.toString().equals("false")){
            return false;
        }else if(obj.toString().equals("1")){
            return true;
        }else if(obj.toString().equals("0")){
            return false;
        }else{
            return DefaultValue;
        }
    }

    public static boolean convertBool(Object obj){
        if(obj == null || obj.toString().isEmpty()){
            return false;
        }else if(obj.toString().equals("true")){
            return true;
        }else if(obj.toString().equals("false")){
            return false;
        }else if(obj.toString().equals("1")){
            return true;
        }else if(obj.toString().equals("0")){
            return false;
        }else{
            return false;
        }
    }


    private static String verifyId(String id) {
        int count = 0;
        char[] charArr = id.toCharArray();
        for (int i = 0; i < charArr.length; i++) {

            int n = Integer.parseInt(charArr[i] + "");
            count += n * (Math.pow(2, 15 - i) % 11);
        }

        switch (count % 11) {
            case 0:
                return "1";
            case 1:
                return "0";
            default:
                return 11 - (count % 11) + "";
        }
    }

    public static String getSno(){
        int i = (int)(Math.random()*9);
        Date date = new Date();
        String randomStr = Integer.toString(i);
        String sno = String.valueOf(date.getTime()) + randomStr;
        return sno + CommUtils.verifyId(sno);
    }

    public static boolean checkPhoneNumber(String phoneNumber){
        String pattern = "^(((13[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(17[3-8]{1})|(18[0-9]{1})|(19[0-9]{1})|(14[5-7]{1}))+\\d{8})$";
        return Pattern.matches(pattern, phoneNumber);
    }

    public static String parseUrl(String url, String urlType){
        String pattern = "(http://|https://|ftp://).*";
        Matcher matcher = Pattern.compile(pattern).matcher(url);
        String webTreaty = "";
        if (matcher.find()) {
            webTreaty = matcher.group(1);
        }
        if (urlType.equals("treaty")){
            return webTreaty.replace("//", "");
        }
        String[] urlPath = url.replace(webTreaty,"").split("/");
        if (urlType.equals("base")){
            return webTreaty + urlPath[0];
        } else if (urlType.equals("host")){
            String hostPattern = "(?i)^https?://(?:[\\w\\-]*\\.)*?([\\w\\-]*\\.(?:com\\.cn|edu\\.cn|org\\.cn|vip\\.cn|top\\.cn" +
                    "|ai\\.cn|cc\\.cn|github\\.io|gov\\.cn|cn|com|net|org|vip|xin|top|club|xyz|wang|win|ai" +
                    "|tw|jp|cc|io))[\\\\/]*";
            Matcher hostMatcher = Pattern.compile(hostPattern).matcher(url);
            if (hostMatcher.find()) {
                return hostMatcher.group();
            }
        } else if (urlType.startsWith("real")) {
            String retUrl = Arrays.stream(urlPath).limit(urlPath.length - 1).collect(Collectors.joining("/"));
            if (urlType.equals("real")) {

                return webTreaty + retUrl.replaceAll("/#.*", "");
            }else if (urlType.equals("realArray")) {
                return webTreaty + "|" + retUrl.replaceAll("/#.*", "");
            }

        }
        return null;
    }

    public static String parseSrc(String sourceSrc, String currentUrl){
        if (sourceSrc.matches("^http.*") || sourceSrc.matches("^ftp.*")){
            return sourceSrc;
        } else if (sourceSrc.startsWith("//")){
            return parseUrl(currentUrl, "treaty") + sourceSrc;
        } else if (sourceSrc.startsWith("/")) {
            return parseUrl(currentUrl, "base") + sourceSrc;
        } else if (sourceSrc.startsWith("#/")) {
            return parseUrl(currentUrl, "base") + sourceSrc.substring(1);
        } else if (sourceSrc.startsWith("./")) {
            return parseUrl(currentUrl, "real") + sourceSrc.substring(1);
        } else if (sourceSrc.startsWith("../")) {
            String[] temp = Objects.requireNonNull(parseUrl(currentUrl, "realArray")).split("\\|");
            boolean flag = true;
            int endFlag = 0;
            while (flag) {
                if (sourceSrc.startsWith("../")) {
                    sourceSrc = sourceSrc.substring(sourceSrc.indexOf("../") + 3 );
                    endFlag = endFlag + 1;
                } else {
                    flag = false;
                }
            }
            for (int i = 0; i < endFlag; i++) {
                temp[1] = temp[1].substring(0, temp[1].lastIndexOf("/"));
            }
            return temp[0] + temp[1] + '/' + sourceSrc;
        } else if (!sourceSrc.matches("^data:image.*")) {
            return parseUrl(currentUrl, "real") + "/" + sourceSrc;
        }
        return null;
    }
}
