package com.bot.patrol.info.dispatch.service;


import com.bot.patrol.info.dispatch.infrastructure.constants.CommonResult;
import com.bot.patrol.info.dispatch.model.page.QuartzLogPage;
import com.bot.patrol.info.dispatch.model.req.*;

/**
* <AUTHOR>
* @date 2022-03-03
*/

public interface IQuartzLogService {

    CommonResult add(QuartzLogAddReq request);

    CommonResult update(QuartzLogUpdateReq request);

    CommonResult list(QuartzLogSearchReq request);

    CommonResult page(QuartzLogPage request);
}