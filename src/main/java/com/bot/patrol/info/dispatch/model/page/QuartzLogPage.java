package com.bot.patrol.info.dispatch.model.page;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
* <AUTHOR>
* @date 2022-03-03
*/
@ApiModel(value=" 定时任务执行历史分页查询",description=" 定时任务执行历史分页查询")
@Data
public class QuartzLogPage extends Page   {
        private Long id;
        private String baenName;
        private String cronExpression;
        private String exceptionDetail;
        private Boolean isSuccess;
        private String jobName;
        private String methodName;
        private String params;
        private Long time;


}
