package com.bot.patrol.info.dispatch.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.bot.patrol.info.dispatch.conver.CaseInfoConvert;
import com.bot.patrol.info.dispatch.conver.TaskInfoConvert;
import com.bot.patrol.info.dispatch.entity.CaseInfo;
import com.bot.patrol.info.dispatch.entity.QuartzJob;
import com.bot.patrol.info.dispatch.entity.TaskInfo;
import com.bot.patrol.info.dispatch.infrastructure.constants.AppConstant;
import com.bot.patrol.info.dispatch.infrastructure.constants.CommonResult;
import com.bot.patrol.info.dispatch.infrastructure.constants.EntityTypeEnum;
import com.bot.patrol.info.dispatch.infrastructure.utils.JsonUtils;
import com.bot.patrol.info.dispatch.infrastructure.utils.OkHttpUtils;
import com.bot.patrol.info.dispatch.infrastructure.utils.RedisUtils;
import com.bot.patrol.info.dispatch.mapper.CaseInfoMapper;
import com.bot.patrol.info.dispatch.mapper.TaskInfoMapper;
import com.bot.patrol.info.dispatch.model.req.CaseInfoUpdateReq;
import com.bot.patrol.info.dispatch.model.req.TaskInfoUpdateReq;
import com.bot.patrol.info.dispatch.schediule.RunTask;
import com.bot.patrol.info.dispatch.service.HttpApiService;
import com.bot.patrol.info.dispatch.service.ICaseInfoService;
import com.bot.patrol.info.dispatch.service.IQuartzJobService;
import com.bot.patrol.info.dispatch.service.ITaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class TaskServiceImpl implements ITaskService {
    @Value("${app.baseUrl}")
    private String patrolDataCenterUrl;
    private final String taskInterfaceUrl =  "task-info/getRunTaskBySid";
    private final ICaseInfoService caseInfoService;
    private final TaskInfoMapper taskInfoMapper;
    private final IQuartzJobService quartzJobService;

    private final CaseInfoMapper caseInfoMapper;
    private final RabbitTemplate rabbitTemplate;
    @Value("${customs.task.rabbitmq-direct-task-control}")
    private String direct;

    @Autowired
    private HttpApiService httpApiService;

    @Autowired
    private ICaseInfoService iCaseInfoService;

    @Autowired
    private RedisUtils redisUtils;

    @Override
    public void addTask(TaskInfo taskInfo) {

    }

    @Override
    public void upTask(TaskInfo taskInfo) {

    }

    @Override
    public void addSchediuleTask(String taskSid) {
        JSONObject getTaskJo = new JSONObject();
        getTaskJo.put("sid", taskSid);
        String ret = OkHttpUtils.httpPostJson(patrolDataCenterUrl+taskInterfaceUrl, AppConstant.DATA_CENTER_TOKEN_HEADER, getTaskJo.toString());
        TaskInfo taskInfo = null;
        List<CaseInfo> caseInfoList = new ArrayList<>();
        if(Objects.nonNull(ret) && JSONObject.fromObject(ret).getInt("code") == 200){
            taskInfo = TaskInfoConvert.INSTANCE.convert(JsonUtils.jsonToMap(JSONObject.fromObject(ret).getString("data")));
            taskInfo.setSid(taskSid);
            List<Map<String,Object>> runCaseList = (List<Map<String, Object>>) ((Map)JsonUtils.jsonToMap(ret).get("data")).get("runCaseList");
            if(Objects.nonNull(runCaseList) && runCaseList.size() > 0) {
                //删除所有old数据
                caseInfoMapper.deleteByTaskId(taskSid);
                for (int i = 0; i < runCaseList.size(); i++) {
                    //此处只有新增
                    CaseInfo caseInfo = CaseInfoConvert.INSTANCE.convert(runCaseList.get(i));
                    caseInfoMapper.add(caseInfo);
                    caseInfoList.add(caseInfo);
                }
                TaskInfo oldTaskInfo = taskInfoMapper.selectBySid(taskSid);
                if(Objects.isNull(oldTaskInfo)){
                    taskInfoMapper.add(taskInfo);
                }else{
                    taskInfoMapper.update(taskInfo);
                }
            }
        }
        LocalDateTime localDateTime = taskInfo.getTiStartTime();
        LocalDateTime startTime = LocalDateTime.of(localDateTime.getYear(), localDateTime.getMonth(), localDateTime.getDayOfMonth(),0,0,0);
        QuartzJob quartzJob = new QuartzJob();
        quartzJob.setJobName(taskInfo.getSid());
        quartzJob.setRemark(taskInfo.getTiDescr());
        quartzJob.setTaskType(taskInfo.getTiRunType());
        quartzJob.setCycleType(taskInfo.getTiRunCycle());
        quartzJob.setCycleNum(taskInfo.getTiCycleNum());
        quartzJob.setBeanName("runTask");
        quartzJob.setMethodName("run");
        List<String> caseList = new ArrayList<>();
        for (int i = 0; i < caseInfoList.size(); i++) {
            caseList.add(caseInfoList.get(i).getSid());
        }
        quartzJob.setParams(JsonUtils.objectToJson(caseList));
        quartzJob.setIsUsing(true);
        quartzJob.setStartTime(taskInfo.getTiStartTime());
        quartzJob.setEndTime(LocalDateTime.of(taskInfo.getTiEndTime().getYear(),taskInfo.getTiEndTime().getMonthValue(), taskInfo.getTiEndTime().getDayOfMonth(),23,59,59));
        if(Objects.nonNull(taskInfo) && caseInfoList.size() > 0) {
            if (taskInfo.getTiRunType() == EntityTypeEnum.TaskRunTypeEnum.TIMING.getCode()) {
                if (Duration.between(LocalDateTime.now(), startTime).toSeconds() <= 0) {
                    RunTask.run(quartzJob.getParams());
                } else {
                    String day = " " + localDateTime.getDayOfMonth();
                    String month = " " + localDateTime.getMonthValue();
                    String dayOfWeek = " ?";
                    String year = " " + localDateTime.getYear();
                    quartzJob.setCronExpression("0 0 0" + day + month + dayOfWeek + year);
                    quartzJobService.insert(quartzJob);
                }
            } else if (taskInfo.getTiRunType() == EntityTypeEnum.TaskRunTypeEnum.CYCLE.getCode()) {
                if (taskInfo.getTiRunCycle() == EntityTypeEnum.CycleTypeEnum.Day.getCode()) {
                    if (Duration.between(LocalDateTime.now(), startTime).toSeconds() <= 0) {
                        RunTask.run(quartzJob.getParams());
                    }
                    quartzJob.setCronExpression("0 0 0 " + (taskInfo.getTiCycleNum() == 0 ? "*" : "*/" + taskInfo.getTiCycleNum()) + " * ? *");
                    pushJob(quartzJob);

                } else if (taskInfo.getTiRunCycle() == EntityTypeEnum.CycleTypeEnum.Week.getCode()) {
                    if (Duration.between(LocalDateTime.now(), startTime).toSeconds() <= 0) {
                        if (LocalDateTime.now().getDayOfWeek().ordinal() + 1 <= taskInfo.getTiCycleNum()) {
                            RunTask.run(quartzJob.getParams());
                        }
                    } else {
                        quartzJob.setCronExpression("0 0 0 ? * " + (taskInfo.getTiCycleNum() == 7 ? "1" : taskInfo.getTiCycleNum() + 1) + " *");
                        pushJob(quartzJob);
                    }

                } else if (taskInfo.getTiRunCycle() == EntityTypeEnum.CycleTypeEnum.Month.getCode()) {
                    if (Duration.between(LocalDateTime.now(), startTime).toSeconds() <= 0) {
                        if (taskInfo.getTiCycleNum() != 0) {
                            if (LocalDateTime.now().getDayOfMonth() <= taskInfo.getTiCycleNum()) {
                                RunTask.run(quartzJob.getParams());
                            }
                        } else {
                            Calendar cal = Calendar.getInstance();
                            cal.set(LocalDateTime.now().getYear(), LocalDateTime.now().getMonthValue(), 0);
                            if (LocalDateTime.now().getDayOfMonth() == cal.get(Calendar.DAY_OF_MONTH)) {
                                RunTask.run(quartzJob.getParams());
                            }
                        }

                    } else {
                        quartzJob.setCronExpression("0 0 0 " + (taskInfo.getTiCycleNum() == 0 ? "L" : taskInfo.getTiCycleNum()) + " * ? *");
                        pushJob(quartzJob);
                    }
                } else if (taskInfo.getTiRunCycle() == EntityTypeEnum.CycleTypeEnum.Interval.getCode()) {
                    if (taskInfo.getTiCycleNum() > 0 || taskInfo.getTiCycleNum() < 24) {
                        quartzJob.setCronExpression("0 0 */" + taskInfo.getTiCycleNum() + " * * ? *");
                        RunTask.intervalJobPush(quartzJob);
                    } else {
                        if (taskInfo.getTiCycleNum() == 0) {
                            quartzJob.setCronExpression("0 * * * * ?");
                            RunTask.intervalJobPush(quartzJob);
                        } else if (taskInfo.getTiCycleNum() == 24) {
                            if (Duration.between(LocalDateTime.now(), startTime).toSeconds() <= 0) {
                                RunTask.run(quartzJob.getParams());
                            } else {
                                quartzJob.setCronExpression("0 0 0 */1 * ?");
                                pushJob(quartzJob);
                            }
                        }
                    }

                } else if (taskInfo.getTiRunCycle() == EntityTypeEnum.CycleTypeEnum.Sustain.getCode()) {
                    if (taskInfo.getTiCycleNum() == 0) {
                        quartzJob.setCronExpression("0 * * * * ?");
                        RunTask.intervalJobPush(quartzJob);
                    } else if (taskInfo.getTiCycleNum() == 1) {
                        quartzJob.setCronExpression("0 * * * * ?");
                        quartzJob.setEndTime(quartzJob.getEndTime().plusYears(10l));
                        RunTask.intervalJobPush(quartzJob);
                    }
                }
            } else if (taskInfo.getTiRunType() == EntityTypeEnum.TaskRunTypeEnum.INSTANT.getCode()) {
                quartzJob.setCronExpression("0 * * * * ?");
                RunTask.run(quartzJob.getParams());
            }
        }
    }

    @Override
    public void stopSchediuleTask(String taskSid) {
        List<CaseInfo> caseInfos=caseInfoService.selectByTaskSid(taskSid);
        for (int i = 0; i < caseInfos.size(); i++) {
            CaseInfo caseInfo=caseInfos.get(i);
            if (Objects.isNull(caseInfo.getCiRuntimeAciSid())){

                CaseInfoUpdateReq caseInfoUpdateReq=new CaseInfoUpdateReq();
                caseInfoUpdateReq.setSid(caseInfo.getSid());
                caseInfoUpdateReq.setCiState(1);
                caseInfoUpdateReq.setCiStatus(EntityTypeEnum.TaskStatusTypeEnum.Stop.getCode());
                caseInfoUpdateReq.setCiLastRunEndTime(LocalDateTime.now());
                CommonResult commonResult1=httpApiService.caseInfoUpdate(caseInfoUpdateReq);
                log.info(com.alibaba.fastjson.JSONObject.toJSONString(commonResult1));
                CaseInfo dispatchCaseInfo = CaseInfoConvert.INSTANCE.convert(caseInfoUpdateReq);
                iCaseInfoService.update(dispatchCaseInfo);
                TaskInfoUpdateReq taskInfoUpdateReq=new TaskInfoUpdateReq();
                taskInfoUpdateReq.setSid(caseInfo.getCiTiSid());
                List<CaseInfo> caseInfoList = iCaseInfoService.selectByTaskSid(caseInfo.getCiTiSid());
                Optional<CaseInfo> optional = caseInfoList.stream().filter(p ->
                        p.getCiStatus() == EntityTypeEnum.TaskStatusTypeEnum.Start.getCode()).findFirst();
                if (!optional.isPresent()) {
                    taskInfoUpdateReq.setTiStatus(EntityTypeEnum.TaskStatusTypeEnum.Stop.getCode());
                }
                CommonResult commonResult=httpApiService.update(taskInfoUpdateReq);
                log.info(com.alibaba.fastjson.JSONObject.toJSONString(commonResult));
                //判断执行器是否有排队数据 如果有直接执行
                String link=redisUtils.get("Link-"+caseInfo.getSid());
                if(StringUtils.isNotEmpty(link)){
                    redisUtils.deleteByPrex("Link-"+caseInfo.getSid());
                }
                continue;
            }else {
                com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();
                jsonObject.put("command", "stop");
                jsonObject.put("actuatorId", caseInfo.getCiRuntimeAciSid());
                jsonObject.put("sid", caseInfo.getSid());
                jsonObject.put("taskId", caseInfo.getCiTiSid());
                jsonObject.put("name", caseInfo.getCiAciName());
                log.info(direct + '|' + caseInfo.getCiAciQueueFlag() + "testlog-queneflag");
                rabbitTemplate.convertAndSend(direct, caseInfo.getCiAciQueueFlag(), jsonObject.toString());
            }
        }

    }

    private void pushJob(QuartzJob quartzJob){
        if(RunTask.isFirstStart){
            if (Duration.between(LocalDateTime.now(), LocalDateTime.of(LocalDateTime.now().getYear(), LocalDateTime.now().getMonthValue(), LocalDateTime.now().getDayOfMonth(), 22, 0, 55)).toSeconds() < 0) {
                if(RunTask.pushState.equals(EntityTypeEnum.QuartzPushStateEnum.WAIT_PUSH)){
                    quartzJobService.insert(quartzJob);
                }
            }else{
                waitPushing(quartzJob);
            }
        }else{
            if(RunTask.pushState.equals(EntityTypeEnum.QuartzPushStateEnum.WAIT_PUSH)){
                quartzJobService.insert(quartzJob);
            }else if (RunTask.pushState.equals(EntityTypeEnum.QuartzPushStateEnum.PUSHING)) {
                waitPushing(quartzJob);
            }else if (RunTask.pushState.equals(EntityTypeEnum.QuartzPushStateEnum.PUSH_END)){
                if(Objects.isNull(quartzJobService.selectNoPushedByJobName(quartzJob.getJobName()))){
                    quartzJobService.insert(quartzJob);
                    if(LocalDateTime.of(LocalDateTime.now().getYear(), LocalDateTime.now().getMonthValue(),LocalDateTime.now().getDayOfMonth(),0,0,0).plusDays(1l).equals(quartzJob.getStartTime())) {
                        quartzJobService.add(quartzJob);
                    }
                }
            }
        }
    }

    private void waitPushing(QuartzJob quartzJob){
        while (true){
            if(!RunTask.pushState.equals(EntityTypeEnum.QuartzPushStateEnum.PUSH_END)){
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }else{
                if(Objects.isNull(quartzJobService.selectNoPushedByJobName(quartzJob.getJobName()))){
                    quartzJobService.insert(quartzJob);
                    if(LocalDateTime.of(LocalDateTime.now().getYear(), LocalDateTime.now().getMonthValue(),LocalDateTime.now().getDayOfMonth(),0,0,0).plusDays(1l).equals(quartzJob.getStartTime())) {
                        quartzJobService.add(quartzJob);
                    }
                    break;
                }
            }
        }
    }
}
