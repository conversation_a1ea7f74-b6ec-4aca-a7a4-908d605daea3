package com.bot.patrol.info.dispatch.mapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bot.patrol.info.dispatch.model.req.QuartzLogSearchReq;
import com.bot.patrol.info.dispatch.entity.QuartzLog;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
* <AUTHOR>
* @date 2022-03-03
*/

@Mapper
@Repository
public interface QuartzLogMapper extends BaseMapper<QuartzLog>  {
    default List<QuartzLog> selectList(QuartzLogSearchReq quartzLogSearchReq) {
        return selectList(new LambdaQueryWrapper<QuartzLog>()
            .eq(ObjectUtils.isNotEmpty(quartzLogSearchReq.getId()), QuartzLog::getId,quartzLogSearchReq.getId())
            .eq(StringUtils.isNotEmpty(quartzLogSearchReq.getBaenName()), QuartzLog::getBaenName,quartzLogSearchReq.getBaenName())
            .eq(StringUtils.isNotEmpty(quartzLogSearchReq.getCronExpression()), QuartzLog::getCronExpression,quartzLogSearchReq.getCronExpression())
            .eq(StringUtils.isNotEmpty(quartzLogSearchReq.getExceptionDetail()), QuartzLog::getExceptionDetail,quartzLogSearchReq.getExceptionDetail())
            .eq(StringUtils.isNotEmpty(quartzLogSearchReq.getJobName()), QuartzLog::getJobName,quartzLogSearchReq.getJobName())
            .eq(StringUtils.isNotEmpty(quartzLogSearchReq.getMethodName()), QuartzLog::getMethodName,quartzLogSearchReq.getMethodName())
            .eq(StringUtils.isNotEmpty(quartzLogSearchReq.getParams()), QuartzLog::getParams,quartzLogSearchReq.getParams())
            .eq(ObjectUtils.isNotEmpty(quartzLogSearchReq.getTime()), QuartzLog::getTime,quartzLogSearchReq.getTime())
);
    }
}
