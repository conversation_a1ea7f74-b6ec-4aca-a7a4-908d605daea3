package com.bot.patrol.info.dispatch.service.impl;

import com.bot.patrol.info.dispatch.entity.LookBackTask;
import com.bot.patrol.info.dispatch.mapper.LookBackTaskMapper;
import com.bot.patrol.info.dispatch.service.ILookBackTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class LookBackTaskServiceImpl implements ILookBackTaskService {
    private final LookBackTaskMapper lookBackTaskMapper;

    @Override
    public LookBackTask selectBySid(String taskSid) {
        return lookBackTaskMapper.selectById(taskSid);
    }

    @Override
    public LookBackTask selectByTaskNo(String taskNo) {
        return lookBackTaskMapper.selectByTaskNo(taskNo);
    }

    @Override
    public int add(LookBackTask lookBackTask) {
        return lookBackTaskMapper.insert(lookBackTask);
    }

    @Override
    public List<LookBackTask> selectByState(int state) {
        return lookBackTaskMapper.selectByState(state);
    }

    @Override
    public int updateEntity(LookBackTask lookBackTask) {
        return lookBackTaskMapper.updateEntity(lookBackTask);
    }
}
