package com.bot.patrol.info.dispatch.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* <AUTHOR>
* @date 2022-02-14
*/
@ApiModel(value="执行器信息对象搜索",description="执行器信息")
@Data
public class ActuatorInfoSearchReq {
        // id
        @ApiModelProperty(value="id",name="id")
        private String sid;
        // 软件列表
        @ApiModelProperty(value="软件列表",name="软件列表")
        private String aciSoftList;
        @ApiModelProperty(value="类型",name="类型")
        private Integer aciType;


}
