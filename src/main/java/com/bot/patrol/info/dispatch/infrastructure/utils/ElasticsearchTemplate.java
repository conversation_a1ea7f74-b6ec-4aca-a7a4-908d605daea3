package com.bot.patrol.info.dispatch.infrastructure.utils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.DocWriteRequest;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.action.admin.indices.create.CreateIndexResponse;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.admin.indices.get.GetIndexRequest;
import org.elasticsearch.action.admin.indices.settings.put.UpdateSettingsRequest;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.client.*;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.elasticsearch.xcontent.XContentType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@Service
@Slf4j
public class ElasticsearchTemplate {

    @Resource
    private RestHighLevelClient restHighLevelClient;

    private static final String TYPE = "_doc";

    /**
     * 查询某个索引的数据
     *
     * @date 2019/12/12
     * @param indexName 索引名称
     * @param clazz     返回的数据类型
     * @return java.util.List<T> 返回列表
     */
    public <T> List<T> findIndex(String indexName, Class<T> clazz){
        try {
            SearchRequest searchRequest = new SearchRequest(indexName);
            SearchResponse getResponse = restHighLevelClient.search(searchRequest,RequestOptions.DEFAULT);
            SearchHits searchHits = getResponse.getHits();
            List<T> results = new ArrayList<>();
            for(SearchHit hit : searchHits){
                T t = JSON.parseObject(hit.getSourceAsString(),clazz);
                results.add(t);
            }
            return results;
        } catch (IOException e) {
            throw new RuntimeException("findIndex Exception",e);
        }
    }


    /**
     * 根据id获取索引的数据
     *
     * @date 2019/12/12
     * @param indexNames    索引名称
     * @param id            id
     * @param clazz         返回的结果类型class
     * @return T            返回对应结果类型的结果
     */
    public <T> T getById(String indexNames, String id,Class<T> clazz) {
        try {
            GetRequest request = new GetRequest(indexNames, TYPE, id);
            GetResponse getReponse = restHighLevelClient.get(request,RequestOptions.DEFAULT);
            if(getReponse.isExists()) {
                String result = getReponse.getSourceAsString();
                T t = JSONObject.parseObject(result,clazz);
                return t;
            }
        }catch (Exception e){
            throw new RuntimeException("getById exception",e);
        }
        return null;
    }

    /**
     * 根据ids批量获取数据，默认根据id排序
     *
     * @date 2019/12/12
     * @param indexNames    索引名称
     * @param clazz         返回类型
     * @param ids           id数组
     * @return java.util.List<T>
     */
    public <T> List<T>  getByIds(String indexNames, Class<T> clazz,String [] ids) {
        try {
            SearchRequest searchRequest = new SearchRequest(indexNames);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            QueryBuilder queryBuilder = QueryBuilders.idsQuery().addIds(ids);
            sourceBuilder.query(queryBuilder);
            sourceBuilder.size(ids.length);
            //id排序
            sourceBuilder.sort(new FieldSortBuilder("_id"));
            searchRequest.source(sourceBuilder);
            searchRequest.types(TYPE);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest,RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            List<T> results = new ArrayList<>();
            for(SearchHit hit : searchHits){
                T t = JSON.parseObject(hit.getSourceAsString(),clazz);
                results.add(t);
            }
            return results;
        }catch (Exception e){
            throw new RuntimeException("multiGetByIds exception",e);
        }
    }

    /**
     * 根据ids分页获取数据
     *
     * @date 2019/12/12
     * @param indexNames    索引名称
     * @param clazz         类型
     * @param ids           id数组
     * @param startIndex    开始index
     * @param pageSize      每页显示多少数据
     * @return java.util.List<T>
     */
    public <T> List<T>  getByIds(String indexNames, Class<T> clazz, String[] ids, int startIndex, int pageSize) {
        try {
            SearchRequest searchRequest = new SearchRequest(indexNames);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            QueryBuilder queryBuilder = QueryBuilders.idsQuery().addIds(ids);
            sourceBuilder.query(queryBuilder);
            sourceBuilder.from(startIndex);
            sourceBuilder.size(pageSize);
            //id排序
            sourceBuilder.sort(new FieldSortBuilder("_id"));
            searchRequest.source(sourceBuilder);
            searchRequest.types(TYPE);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest,RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            List<T> results = new ArrayList<>();
            for(SearchHit hit : searchHits){
                T t = JSON.parseObject(hit.getSourceAsString(),clazz);
                results.add(t);
            }
            return results;
        }catch (Exception e){
            throw new RuntimeException("multiGetByIds exception",e);
        }
    }

    /**
     * 排序分页
     *
     * @date 2019/12/12
     * @param indexNames 索引名称
     * @param clazz      类型
     * @param sortName   排序名称
     * @param order      正倒序
     * @param start      开始位置
     * @param size       每页条数
     * @return java.util.List<T>
     */
    public <T> List<T> findPageBySort(String indexNames, Class<T> clazz,String sortName,SortOrder order, int start, int size) {
        try {
            SearchRequest searchRequest = new SearchRequest(indexNames);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.from(start);
            sourceBuilder.size(size);
            //rank排序
            sourceBuilder.sort(new FieldSortBuilder(sortName).order(order));
            searchRequest.source(sourceBuilder);
            searchRequest.types(TYPE);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest,RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            List<T> results = new ArrayList<>();
            for(SearchHit hit : searchHits){
                T t = JSON.parseObject(hit.getSourceAsString(),clazz);
                results.add(t);
            }
            return results;
        }catch (Exception e){
            throw new RuntimeException("multiGetByIds exception",e);
        }
    }



    /**
     * 搜索分页
     *
     * @date 2019/12/12
     * @param indexNames    索引名称
     * @param pageStart     分页开始位置
     * @param pageSize      每页显示条数
     * @param sortBuilders  排序Builder
     * @param queryBuilder  查询Builder
     * @param clazz         返回类型
     * @return java.util.List<T>
     */
    public <T> List<T>findPageSearch(String indexNames, Integer pageStart, Integer pageSize, List<SortBuilder> sortBuilders, QueryBuilder queryBuilder, Class<T> clazz) {
        try {
            SearchRequest searchRequest = new SearchRequest(indexNames);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            if(pageStart != null) {
                sourceBuilder.from(pageStart);
                sourceBuilder.size(pageSize);
            }
            sourceBuilder.query(queryBuilder);
            if(sortBuilders != null) {
                //rank排序
                for (SortBuilder builder : sortBuilders) {
                    sourceBuilder.sort(builder);
                }
            }
            searchRequest.source(sourceBuilder);
            searchRequest.types(TYPE);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest,RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            List<T> results = new ArrayList<>();
            for(SearchHit hit : searchHits){
                T t = JSON.parseObject(hit.getSourceAsString(),clazz);
                results.add(t);
            }
            return results;
        }catch (Exception e){
            throw new RuntimeException("multiGetByIds exception",e);
        }
    }

    /**
     * 判断索引是否存在
     *
     * @date 2019/12/10
     * @param esIndex 索引名
     * @return boolean
     */
    public boolean existsIndex(String esIndex) {
        boolean exist = false;
        try {
            GetIndexRequest indexRequest = new GetIndexRequest().indices(esIndex);
            exist = restHighLevelClient.indices().exists(indexRequest,RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException("existsIndex exception",e);
        }
        return exist;
    }

    /**
     * 创建索引
     *
     * @date 2019/12/11
     * @param index 索引名称
     * @param settings setting设置，可空
     * @param properties properties设置
     * @return boolean
     */
    public boolean createIndex(String index,Map<String,Object> settings,Map<String,Map<String,String>> properties){
        //创建索引请求对象，并设置索引名称
        CreateIndexRequest createIndexRequest = new CreateIndexRequest(index);
        //设置索引参数
        Settings.Builder builder = Settings.builder();
        if(!CollectionUtils.isEmpty(settings)){
            for(Map.Entry<String,Object> entry : settings.entrySet()){
                if(entry.getValue() instanceof String){
                    builder.put(entry.getKey(),(String) entry.getValue());
                }else if(entry.getValue() instanceof Integer){
                    builder.put(entry.getKey(),(Integer) entry.getValue());
                }
            }
            createIndexRequest.settings(builder);
        }
        if(!CollectionUtils.isEmpty(properties)) {
            JSONObject sourceJson = new JSONObject();
            sourceJson.put("properties", JSONObject.toJSON(properties));
            //设置映射
            createIndexRequest.mapping(TYPE, sourceJson.toJSONString(), XContentType.JSON);
        }
        //创建索引操作客户端
        IndicesClient indices = restHighLevelClient.indices();
        try {
            //创建响应对象
            CreateIndexResponse createIndexResponse = indices.create(createIndexRequest,RequestOptions.DEFAULT);
            return createIndexResponse.isAcknowledged();
        } catch (IOException e) {
            log.error("创建索引异常",e);
            throw new RuntimeException("创建索引异常",e);
        }
    }

    /**
     * 删除索引
     *
     * @date 2019/12/11
     * @param indexName 索引名称
     * @return boolean
     */
    public boolean deleteIndex(String indexName) {
        try {
            DeleteIndexRequest request = new DeleteIndexRequest(indexName);
            AcknowledgedResponse deleteIndexResponse = restHighLevelClient.indices().delete(request,RequestOptions.DEFAULT);
            return deleteIndexResponse.isAcknowledged();
        }catch (Exception e){
            log.error("删除索引{}异常",indexName,e);
            throw new RuntimeException("删除索引异常",e);
        }
    }

    /**
     * 删除索引
     *
     * @date 2019/12/11
     * @param indexNames 索引名称
     * @return boolean
     */
    public boolean deleteIndex(String ... indexNames) {
        try {
            DeleteIndexRequest request = new DeleteIndexRequest(indexNames);
            AcknowledgedResponse deleteIndexResponse = restHighLevelClient.indices().delete(request,RequestOptions.DEFAULT);
            return deleteIndexResponse.isAcknowledged();
        }catch (Exception e){
            log.error("删除索引{}异常",indexNames,e);
            throw new RuntimeException("删除索引异常",e);
        }
    }

    /**
     * 修改索引的settings
     *
     * @date 2019/12/11
     * @param indexName 索引名称
     * @param settings 索引settings
     * @return boolean
     */
    public boolean putSetting(String indexName,Map<String,Object> settings){
        UpdateSettingsRequest request = new UpdateSettingsRequest(indexName);
        request.settings(settings);
        try {
            AcknowledgedResponse updateSettingsResponse = restHighLevelClient.indices().putSettings(request,RequestOptions.DEFAULT);
            return updateSettingsResponse.isAcknowledged();
        } catch (IOException e) {
            log.error("索引修改setting异常",e);
            throw new RuntimeException("索引修改setting异常",e);
        }
    }

    /**
     * 插入或更新索引数据
     *
     * @date 2019/12/12
     * @param indexName     索引名称
     * @param id            id的名称
     * @param data          插入的数据
     * @return String       返回插入或者更新的id
     */
    public <T> String insert(String indexName,String id,T data){
        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(data);
        IndexRequest indexRequest = new IndexRequest(indexName, TYPE, jsonObject.getString(id))
                .source(jsonObject.toJSONString(), XContentType.JSON);
        IndexResponse indexResponse = null;
        try {
            indexResponse = restHighLevelClient.index(indexRequest,RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("es插入异常",e);
            throw  new RuntimeException("es插入异常",e);
        }
        if (indexResponse.getResult() == DocWriteResponse.Result.CREATED) {
            return  indexResponse.getId();
        } else if (indexResponse.getResult() == DocWriteResponse.Result.UPDATED) {
            return indexResponse.getId();
        }
        return null;
    }

    /**
     * 批量插入索引数据
     *
     * @date 2019/12/11
     * @param indexName     索引名称
     * @param id            索引的id名称
     * @param  list         插入的数据
     * @return boolean
     */
    public <T>  boolean batchInsert(String indexName,String id,List<T> list){
        BulkRequest request = new BulkRequest();
        //空数据直接返回成功
        if(CollectionUtils.isEmpty(list)){
            return true;
        }
        for(T object : list){
            JSONObject jsonObject = (JSONObject) JSONObject.toJSON(object);
            String json = jsonObject.toString(SerializerFeature.WriteNullNumberAsZero);
            request.add(new IndexRequest(indexName, TYPE, jsonObject.getString(id))
                    .source(json,XContentType.JSON));
        }
        BulkResponse bulkResponse = null;
        try {
            bulkResponse = restHighLevelClient.bulk(request,RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("批量插入es异常",e);
            throw new RuntimeException("批量插入es异常",e);
        }
        //如果返回结果有一项是失败
        if(bulkResponse != null && bulkResponse.hasFailures()){
            for (BulkItemResponse bulkItemResponse : bulkResponse) {
                //因为是添加操作，所以只处理添加的响应
                if (bulkItemResponse.getOpType() == DocWriteRequest.OpType.INDEX
                        || bulkItemResponse.getOpType() == DocWriteRequest.OpType.CREATE) {
                    if (bulkItemResponse.isFailed()) {
                        BulkItemResponse.Failure failure = bulkItemResponse.getFailure();
                        log.error("批量插入索引失败:{}",failure);
                    }
                }
            }
            return false;
        }else if(bulkResponse == null){
            return false;
        }
        return true;
    }
}


