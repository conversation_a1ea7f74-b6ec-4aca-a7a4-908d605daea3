package com.bot.patrol.info.dispatch.model.resp;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022-02-08
 */
@Data
public class TaskInfoResp {
    private String sid;
    // 项目id
    private String tiPiSid;
    // 用户id
    private String tiCpiSid;
    // 任务用户实体名称
    private String tiCpiName;
    // 任务类型
    private Integer tiType;
    // 任务标识
    private String tiSno;
    // 任务描述
    private String tiDescr;
    // 归属行业
    private String tiIndustryType;
    // 任务运行类型
    private Integer tiRunType;
    // 任务周期运行类型
    private Integer tiRunCycle;
    // 任务周期运行值
    private Integer tiCycleNum;
    // 任务计划开始时间
    private LocalDateTime tiStartTime;
    // 任务计划结束时间
    private LocalDateTime tiEndTime;
    // 最小占用执行器数量
    private Integer tiMinActuator;
    // 最大占用执行器数量
    private Integer tiMaxActuator;
    // 执行优先级
    private Integer tiPriority;
    // 运行次数
    private Integer tiRunNum;
    // 任务运行状态
    private Integer tiStatus;
    // 是否停用 0-否 1-是
    private Integer tiState;
    // 任务创建时间
    private LocalDateTime tiCreateTime;
    // 任务更新时间
    private LocalDateTime tiUpdateTime;
}
