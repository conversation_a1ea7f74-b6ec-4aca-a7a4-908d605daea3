package com.bot.patrol.info.dispatch.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
* <AUTHOR>
* @date 2022-02-08
*/
@ApiModel(value="任务信息对象更新",description="任务信息")
@Data
public class TaskInfoUpdateReq {
        private String sid;

        // 任务运行状态
        @ApiModelProperty(value="任务运行状态（1-执行2-停止）",name="任务运行状态（1-执行2-停止）")
        private Integer tiStatus;


        @ApiModelProperty(value="外部id",name="外部id")
        private String tiExternalId;

        @ApiModelProperty(value="爬虫引擎id",name="爬虫引擎id")
        private String tiCrawlerId;

        // 运行次数
        @ApiModelProperty(value="运行次数",name="运行次数")
        private Integer tiRunNum;

        @ApiModelProperty(value="0-正常 1-警告",name="0-正常 1-警告")
        private Integer tiRunWarning;

        @ApiModelProperty(value="任务类型",name="任务类型")
        @NotNull(message = "不能为空")
        private Integer tiType;

        @ApiModelProperty(value="是否删除0删除1未删除",name="是否删除0删除1未删除")
        private Integer tiDelete;

        private Integer tiState;


}
