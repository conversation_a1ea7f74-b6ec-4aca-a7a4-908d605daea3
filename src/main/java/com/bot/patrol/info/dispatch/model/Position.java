package com.bot.patrol.info.dispatch.model;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="位置信息",description="位置信息")
public class Position {
    private String upPos;
    private String downPos;
    private String leftPos;
    private String rightPos;
}
