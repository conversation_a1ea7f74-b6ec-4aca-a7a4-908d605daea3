package com.bot.patrol.info.dispatch.infrastructure.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TokenStateEnum {
    NORMAL(200, "SUCCESS"),
    OVERDUE(401, "访问权限失效");

    private final int code;
    private final String value;

    //根据key获取枚举
    public static TokenStateEnum getEnumByKey(int key){
        for(TokenStateEnum temp: TokenStateEnum.values()){
            if(temp.getCode() == key){
                return temp;
            }
        }
        return null;
    }
}
