package com.bot.patrol.info.dispatch.receiver;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bot.patrol.info.dispatch.entity.CaseInfo;
import com.bot.patrol.info.dispatch.infrastructure.constants.CommonResult;
import com.bot.patrol.info.dispatch.infrastructure.utils.RedisUtils;
import com.bot.patrol.info.dispatch.model.req.ActuatorInfoUpdateReq;
import com.bot.patrol.info.dispatch.model.req.CaseInfoUpdateReq;
import com.bot.patrol.info.dispatch.schediule.RunTask;
import com.bot.patrol.info.dispatch.service.HttpApiService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;

@Component
@Slf4j
public class ConsumerService {


    @Autowired
    RabbitTemplate rabbitTemplate;

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private HttpApiService httpApiService;
//    @RabbitListener(
//            bindings=@QueueBinding(
//                    value=@Queue(value="${customs.task.rabbitmq-queue-callback}",autoDelete="false"),
//                    exchange=@Exchange(value="${customs.task.rabbitmq-direct-callback}",durable="true", type= ExchangeTypes.DIRECT),
//                    key="${customs.task.rabbitmq-key-callback}"
//            ), concurrency = "50", ackMode = "MANUAL"
//    )
    @RabbitHandler
    public void consumeMessage(Message message, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        String str = new String(message.getBody(), "utf-8");
        try {
            JSONObject jsonObject=JSONObject.parseObject(str);
            String id=jsonObject.getString("id");
            String taskId=jsonObject.getString("taskId");
            String actuatorId=jsonObject.getString("actuatorId");
            /**
             * TERMINATED：被终止   COMPLETED：完成  RUNNING：运行中
             */
            String status=jsonObject.getString("status");
            if("COMPLETED".equals(status)){
                CaseInfoUpdateReq caseInfoUpdateReq=new CaseInfoUpdateReq();
                caseInfoUpdateReq.setSid(id);
                caseInfoUpdateReq.setCiLastRunEndTime(LocalDateTime.now());
                CommonResult commonResult1=httpApiService.caseInfoUpdate(caseInfoUpdateReq);
                log.info("更新最后执行结束时间"+JSONObject.toJSONString(commonResult1));
                //判断执行器是否有排队数据 如果有直接执行
                String link=redisUtils.get("Link-"+id);
                if(StringUtils.isNotEmpty(link)){
                    redisUtils.deleteByPrex("Link-"+id);
                    JSONArray jsonArray=new JSONArray();
                    jsonArray.add(id);
                    RunTask.run(jsonArray.toJSONString());
                }

            }
            if("TERMINATED".equals(status)){
                CaseInfo caseInfo=new CaseInfo();
                caseInfo.setSid(id);
                caseInfo.setCiTiSid(taskId);
                log.info("处理记录");
               //更新脚本状态
                RunTask.dealError(caseInfo);
            }
            if("RUNNING".equals(status)){
                CaseInfoUpdateReq caseInfoUpdateReq=new CaseInfoUpdateReq();
                caseInfoUpdateReq.setSid(id);
                caseInfoUpdateReq.setCiLastRunStartTime(LocalDateTime.now());
                CommonResult commonResult1=httpApiService.caseInfoUpdate(caseInfoUpdateReq);
                log.info("更新最后执行开始时间"+JSONObject.toJSONString(commonResult1));
                ActuatorInfoUpdateReq actuatorInfoUpdateReq=new ActuatorInfoUpdateReq();
                actuatorInfoUpdateReq.setSid(actuatorId);
                actuatorInfoUpdateReq.setAciStatus(3);
                CommonResult commonResult=httpApiService.actuatorUpdate(actuatorInfoUpdateReq);
                log.info("更新任务状态"+JSONObject.toJSONString(commonResult));
                if(commonResult.getCode()!=200){
                    channel.basicNack(deliveryTag, false, true);
                }
            }
            //去解锁执行器
            Boolean b=redisUtils.redisTemplate.delete("Lock-"+actuatorId);
            log.info("解锁是否成功"+b);
            channel.basicAck(deliveryTag, false);
        }catch (Exception e){
            channel.basicNack(deliveryTag, false, true);
            e.printStackTrace();
        }


        log.info("收到的消息:{}",str);
    }

}

