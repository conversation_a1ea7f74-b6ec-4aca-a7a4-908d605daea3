package com.bot.patrol.info.dispatch.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* <AUTHOR>
* @date 2022-02-14
*/
@ApiModel(value="执行器信息对象更新",description="执行器信息")
@Data
public class ActuatorInfoUpdateReq {
        // id
        @ApiModelProperty(value="id",name="id")
        private String sid;
        // 所属实体
        @ApiModelProperty(value="所属实体",name="所属实体")
        private String aciCpiSid;
        // 队列标识
        @ApiModelProperty(value="队列标识",name="队列标识")
        private String aciQueueFlag;
        // 类型
        @ApiModelProperty(value="类型",name="类型")
        private Integer aciType;
        // 执行器访问类型
        @ApiModelProperty(value="执行器访问类型",name="执行器访问类型")
        private Integer aciConnType;
        // 版本
        @ApiModelProperty(value="版本",name="版本")
        private String aciVersion;
        // 名称
        @ApiModelProperty(value="名称",name="名称")
        private String aciName;
        // 执行器地址
        @ApiModelProperty(value="执行器地址",name="执行器地址")
        private String aciIp;
        // 执行器访问端口
        @ApiModelProperty(value="执行器访问端口",name="执行器访问端口")
        private Integer aciPort;
        // 执行器设备标识
        @ApiModelProperty(value="执行器设备标识",name="执行器设备标识")
        private String aciDevice;
        // 执行器设备网络标识
        @ApiModelProperty(value="执行器设备网络标识",name="执行器设备网络标识")
        private String aciDeviceWifi;
        // 系统类型
        @ApiModelProperty(value="系统类型",name="系统类型")
        private String aciSystem;
        // 系统架构
        @ApiModelProperty(value="系统架构",name="系统架构")
        private String aciArch;
        // 系统版本
        @ApiModelProperty(value="系统版本",name="系统版本")
        private String aciSystemVersion;
        // cpu信息
        @ApiModelProperty(value="cpu信息",name="cpu信息")
        private String aciCpu;
        // 内存信息
        @ApiModelProperty(value="内存信息",name="内存信息")
        private String aciRam;
        // 0-空闲/1-繁忙/2-等待/3-阻塞/4-异常/5-停止/6-禁用
        @ApiModelProperty(value="0-空闲/1-繁忙/2-等待/3-阻塞/4-异常/5-停止/6-禁用",name="0-空闲/1-繁忙/2-等待/3-阻塞/4-异常/5-停止/6-禁用")
        private Integer aciStatus;
        // 软件列表
        @ApiModelProperty(value="软件列表",name="软件列表")
        private String aciSoftList;
        // 测试用执行器 0-否 1-是
        @ApiModelProperty(value="测试用执行器 0-否 1-是",name="测试用执行器 0-否 1-是")
        private Integer aciIsTest;
        // 操作者
        @ApiModelProperty(value="操作者",name="操作者")
        private String aciOperator;
        // 扩展字段
        @ApiModelProperty(value="扩展字段",name="扩展字段")
        private String ext;

        @ApiModelProperty(value="机房标识",name="机房标识")
        private String aciServiceFlag;


}
