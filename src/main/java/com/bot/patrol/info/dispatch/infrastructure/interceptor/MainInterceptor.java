package com.bot.patrol.info.dispatch.infrastructure.interceptor;

import com.bot.patrol.info.dispatch.infrastructure.constants.AppConstant;
import com.bot.patrol.info.dispatch.infrastructure.exception.UnLoggedException;
import com.bot.patrol.info.dispatch.infrastructure.utils.JsonUtils;
import com.bot.patrol.info.dispatch.infrastructure.utils.RedisUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.NamedThreadLocal;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
@AllArgsConstructor
public class MainInterceptor implements HandlerInterceptor {

    private static final NamedThreadLocal<Long> threadLocal = new NamedThreadLocal<>("Start-Time");
    private final RedisUtils redisUtils;
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        Long startTime = System.currentTimeMillis();
        threadLocal.set(startTime);
        String uri = request.getRequestURI();
        if (StringUtils.isBlank(uri)) {
            throw new UnLoggedException();
        }
        log.info("收到请求:{}", uri);
        if (AppConstant.WHITE_LIST.contains(uri)) {
            return true;
        }
        if(uri.indexOf(".js")>0||uri.indexOf(".css")>0||uri.indexOf("actuator")>0){
            return true;
        }
        String token = request.getHeader(AppConstant.AUTH_TOKEN);
        String webToken = request.getHeader(AppConstant.WEB_TOKEN);

        return true;

    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception exception) {
        log.info("请求:{}处理完成,耗时:{}ms", request.getRequestURL().toString(), System.currentTimeMillis() - threadLocal.get());
    }

    private void sendError(HttpServletResponse response) throws IOException {
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/json; charset=utf-8");
        PrintWriter writer = response.getWriter();
        Map<String, Object> map = new HashMap<>();
        map.put("code", HttpStatus.UNAUTHORIZED.value());
        map.put("msg", HttpStatus.UNAUTHORIZED.getReasonPhrase());
        writer.write(JsonUtils.objectToJson(map));
        writer.close();
    }
}