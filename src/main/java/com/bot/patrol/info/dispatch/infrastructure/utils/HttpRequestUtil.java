package com.bot.patrol.info.dispatch.infrastructure.utils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

/**
 * @ClassName HttpRequestUtil
 * @Description TODO http请求工具类
 * @Date
 * <AUTHOR> @Version
 */
public class HttpRequestUtil {
    /*
     * @Description TODO sendGet 向指定URL发送GET方法的请求
     * @Param    url        发送请求的URL
     * @Param    params     请求参数
     * @Param    headers    请求头参数
     * @Return java.lang.String 所代表远程资源的响应结果
     **/
    public static String sendGet(String url, Map<String, String> headers) {
        String param = "";
        StringBuilder result = new StringBuilder();
        BufferedReader in = null;
        try {
            String urlNameString = url + param;
            URL realUrl = new URL(urlNameString);
            // 打开和URL之间的连接
            URLConnection connection = realUrl.openConnection();
            // 设置通用的请求属性
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            // connection.setRequestProperty("Accept-Charset", "UTF-8");
            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 添加用户设置的请求头
            if (headers != null) {
                Set<Entry<String, String>> headerEntrySet = headers.entrySet();
                for (Entry<String, String> entry : headerEntrySet) {
                    connection.setRequestProperty(entry.getKey(), entry.getValue().toString());
                }
            }

            // 建立实际的连接
            connection.connect();
            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();
            // 遍历所有的响应头字段
            // for (String key : map.keySet()) {
            // System.out.println(key + "--->" + map.get(key));
            // }
            // 定义 BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }

            byte[] bresult = result.toString().getBytes();
            result = new StringBuilder(new String(bresult, StandardCharsets.UTF_8));
        } catch (Exception e) {
            System.out.println("发送GET请求出现异常！" + e);
            e.printStackTrace();
        }
        // 使用finally块来关闭输入流
        finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e2) {
                e2.printStackTrace();
            }
        }

        return result.toString();
    }

    /*
     * @Description TODO sendGet 向指定URL发送GET方法的请求
     * @Param    url        发送请求的URL
     * @Param    params     请求参数
     * @Param    headers    请求头参数
     * @Param    encoding   设置响应信息的编码格式，如utf-8
     * @Return java.lang.String 所代表远程资源的响应结果
     **/
    public static String sendGet(String url, Map<String, Object> params, Map<String, Object> headers, String encoding) {
        String param = "";
        if (params != null) {
            int length = params.size();
            int count = 1;
            Set<Entry<String, Object>> entrySet = params.entrySet();
            for (Entry<String, Object> entry : entrySet) {
                if (count < length) {
                    param += entry.getKey() + "=" + entry.getValue() + "&";
                } else {
                    param += entry.getKey() + "=" + entry.getValue();
                    break;
                }
                count++;
            }
        }
        StringBuilder result = new StringBuilder();
        BufferedReader in = null;
        try {
            String urlNameString = url + "?" + param;
            URL realUrl = new URL(urlNameString);
            // 打开和URL之间的连接
            URLConnection connection = realUrl.openConnection();
            // 设置通用的请求属性
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 添加用户设置的请求头
            if (headers != null) {
                Set<Entry<String, Object>> headerEntrySet = headers.entrySet();
                for (Entry<String, Object> entry : headerEntrySet) {
                    connection.setRequestProperty(entry.getKey(), entry.getValue().toString());
                }
            }
            // 建立实际的连接
            connection.connect();
            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();
            // 遍历所有的响应头字段
            // for (String key : map.keySet()) {
            // System.out.println(key + "--->" + map.get(key));
            // }
            // 定义 BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }

            byte[] bresult = result.toString().getBytes();
            result = new StringBuilder(new String(bresult, encoding));
        } catch (Exception e) {
            System.out.println("发送GET请求出现异常！" + e);
            e.printStackTrace();
        }
        // 使用finally块来关闭输入流
        finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e2) {
                e2.printStackTrace();
            }
        }

        return result.toString();
    }
    /*
     * @Description TODO sendPost 发送POST方法的请求
     * @Param url       发送请求的 URL
     * @Param jsonData 请求参数，请求参数应该是Json格式字符串的形式。
     * @Param headers
     * @Return java.lang.String  所代表远程资源的响应结果
     **/
    public static String sendPost(String url, String jsonData, Map<String, Object> headers) {
        PrintWriter out = null;
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection con = realUrl.openConnection();
            HttpURLConnection conn = (HttpURLConnection) con;
            // 设置通用的请求属性
            conn.setRequestMethod("POST"); // 设置Post请求
            conn.setConnectTimeout(5 * 1000);
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded"); // 设置内容类型
            // 添加用户设置的请求头
            if (headers != null) {
                Set<Entry<String, Object>> headerEntrySet = headers.entrySet();
                for (Entry<String, Object> entry : headerEntrySet) {
                    conn.setRequestProperty(entry.getKey(), entry.getValue().toString());
                }
            }
            // conn.setRequestProperty("Content-Length",
            // String.valueOf(param.length())); //设置长度
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(new OutputStreamWriter(conn.getOutputStream(), StandardCharsets.UTF_8));
            // 发送请求参数
            // out.print(param);
            out.write(jsonData);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            byte[] bresult = result.toString().getBytes();
            result = new StringBuilder(new String(bresult, StandardCharsets.UTF_8));
        } catch (Exception e) {
            System.out.println("发送 POST 请求出现异常！" + e);
            e.printStackTrace();
        }
        // 使用finally块来关闭输出流、输入流
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return result.toString();
    }

    public static String sendPost(String url, String jsonData, Map<String, Object> headers, String encoding,
                                  String authorization, String postmanToken) {
        PrintWriter out = null;
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection con = realUrl.openConnection();
            HttpURLConnection conn = (HttpURLConnection) con;
            // 设置通用的请求属性
            conn.setRequestMethod("POST"); // 设置Post请求
            conn.setConnectTimeout(5 * 1000);
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded"); // 设置内容类型
            conn.setRequestProperty("authorization", authorization);
            conn.setRequestProperty("postman-token", postmanToken);
            // 添加用户设置的请求头
            if (headers != null) {
                Set<Entry<String, Object>> headerEntrySet = headers.entrySet();
                for (Entry<String, Object> entry : headerEntrySet) {
                    conn.setRequestProperty(entry.getKey(), entry.getValue().toString());
                }
            }
            // conn.setRequestProperty("Content-Length",
            // String.valueOf(param.length())); //设置长度
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(new OutputStreamWriter(conn.getOutputStream(), encoding));
            // 发送请求参数
            // out.print(param);
            out.write(jsonData);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            byte[] bresult = result.toString().getBytes();
            result = new StringBuilder(new String(bresult, encoding));
        } catch (Exception e) {
            System.out.println("发送 POST 请求出现异常！" + e);
            e.printStackTrace();
        }
        // 使用finally块来关闭输出流、输入流
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return result.toString();
    }

    /*
     * @Description TODO sendPost 向指定 URL 发送POST方法的请求
     * @Param    url        发送请求的 URL
     * @Param    jsonData   请求参数，请求参数应该是Json格式字符串的形式。
     * @Param    headers    设置响应信息的编码格式，如utf-8
     * @Param    encoding
     * @Return java.lang.String 所代表远程资源的响应结果
     **/
    public static String sendPost(String url, String jsonData, Map<String, Object> headers, String encoding) {
        PrintWriter out = null;
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection con = realUrl.openConnection();
            HttpURLConnection conn = (HttpURLConnection) con;
            // 设置通用的请求属性
            conn.setRequestMethod("POST"); // 设置Post请求
            conn.setConnectTimeout(5 * 1000);
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded"); // 设置内容类型
            // 添加用户设置的请求头
            if (headers != null) {
                Set<Entry<String, Object>> headerEntrySet = headers.entrySet();
                for (Entry<String, Object> entry : headerEntrySet) {
                    conn.setRequestProperty(entry.getKey(), entry.getValue().toString());
                }
            }
            // conn.setRequestProperty("Content-Length",
            // String.valueOf(param.length())); //设置长度
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(new OutputStreamWriter(conn.getOutputStream(), encoding));
            // 发送请求参数
            // out.print(param);
            out.write(jsonData);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            byte[] bresult = result.toString().getBytes();
            result = new StringBuilder(new String(bresult, encoding));
        } catch (Exception e) {
            System.out.println("发送 POST 请求出现异常！" + e);
            e.printStackTrace();
        }
        // 使用finally块来关闭输出流、输入流
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return result.toString();
    }

    /*
     * @Description TODO uploadPost 上传媒体文件
     * @Param    url    上传媒体文件的微信公众平台接口地址
     * @Param    file   要上传的媒体文件对象
     * @Return java.lang.String 返回上传的响应结果，详情参看微信公众平台开发者接口文档
     **/
    public static String uploadPost(String url, File file) {
        DataOutputStream dos = null;
        FileInputStream fis = null;
        DataInputStream dis = null;
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();
        String end = "\r\n";
        String twoHyphens = "--"; // 用于拼接
        String boundary = "*****"; // 用于拼接 可自定义
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection con = realUrl.openConnection();
            HttpURLConnection conn = (HttpURLConnection) con;
            // 设置通用的请求属性
            conn.setRequestMethod("POST"); // 设置Post请求
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setConnectTimeout(5 * 1000);
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("Charset", "UTF-8");
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary); // 设置内容类型

            // 获取URLConnection对象对应的输出流
            dos = new DataOutputStream(conn.getOutputStream());
            // 1、写入媒体头部分
            String sb = twoHyphens + boundary + end +
                    "Content-Disposition: form-data;name=\"file\";filename=\"" + file.getName() + "\"" + end +
                    "Content-Type:application/octet-stream" + end + end;
            byte[] head = sb.getBytes(StandardCharsets.UTF_8);
            dos.write(head);

            // 2、写入媒体正文部分， 对文件进行传输
            fis = new FileInputStream(file);
            dis = new DataInputStream(fis);
            byte[] buffer = new byte[8192]; // 8K
            int count;
            while ((count = dis.read(buffer)) != -1) {
                dos.write(buffer, 0, count);
            }

            // 3、写入媒体结尾部分。
            byte[] foot = (end + twoHyphens + boundary + twoHyphens + end).getBytes(StandardCharsets.UTF_8);
            dos.write(foot);
            dos.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            byte[] bresult = result.toString().getBytes();
            result = new StringBuilder(new String(bresult, StandardCharsets.UTF_8));
        } catch (Exception e) {
            System.out.println("发送 POST 请求出现异常！" + e);
            e.printStackTrace();
        }
        // 使用finally块来关闭输出流、输入流
        finally {
            try {
                if (dos != null) {
                    dos.close();
                }
                if (dis != null) {
                    dis.close();
                }
                if (fis != null) {
                    fis.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return result.toString();
    }

}
