package com.bot.patrol.info.dispatch.service.impl;

import com.bot.patrol.info.dispatch.entity.LookBackUrlList;
import com.bot.patrol.info.dispatch.mapper.LookBackUrlListMapper;
import com.bot.patrol.info.dispatch.service.ILookBackUrlListService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class LookBackUrlListServiceImpl implements ILookBackUrlListService {
    private final LookBackUrlListMapper lookBackUrlListMapper;

    @Override
    public LookBackUrlList selectBySid(String itemSid) {
        return lookBackUrlListMapper.selectById(itemSid);
    }

    @Override
    public int add(LookBackUrlList lookBackUrlList) {
        return lookBackUrlListMapper.insert(lookBackUrlList);
    }

    @Override
    public List<LookBackUrlList> selectByTaskSid(String taskSid) {
        return lookBackUrlListMapper.selectByTaskSid(taskSid);
    }

    @Override
    public boolean getFinishStateByTaskSid(String taskSid) {
        return lookBackUrlListMapper.getFinishStateByTaskSid(taskSid);
    }

    @Override
    public int updateEntity(LookBackUrlList lookBackUrlList) {
        return lookBackUrlListMapper.updateEntity(lookBackUrlList);
    }

    @Override
    public List<LookBackUrlList> selectByTaskSidAndUrl(String taskSid, String url) {
        return lookBackUrlListMapper.selectByTaskSidAndUrl(taskSid, url);
    }


}
