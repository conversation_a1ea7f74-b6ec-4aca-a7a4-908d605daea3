package com.bot.patrol.info.dispatch.infrastructure.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

public class JsonUtils {

    private static final ObjectMapper JSON_MAPPER = new ObjectMapper();

    public static <T> String objectToJson(T data) {
        String json = null;
        if (data != null) {
            try {
                json = JSON_MAPPER.writeValueAsString(data);
            } catch (Exception e) {
                throw new RuntimeException("objectToJson method error: " + e);
            }
        }
        return json;
    }

    public static <T> T jsonToObject(String json, Class<T> cls) {
        T object = null;
        if (StringUtils.isNotEmpty(json) && cls != null) {
            try {
                if (json.startsWith("\"{")) {
                    json = json.replace("\"{", "{").replace("}\"", "}").replace("\\\"", "\"");
                }
                object = JSON_MAPPER.readValue(json, cls);
            } catch (Exception e) {
                throw new RuntimeException("jsonToObject method error: " + e);
            }
        }
        return object;
    }

    public static List<Map<String, Object>> jsonToList(String json) {
        List<Map<String, Object>> list = null;
        if (StringUtils.isNotEmpty(json)) {
            try {
                if (json.startsWith("\"[")) {
                    json = json.replace("\"[", "[").replace("]\"", "]").replace("\\\"", "\"");
                }
                list = JSON_MAPPER.readValue(json, List.class);
            } catch (Exception e) {
                throw new RuntimeException("jsonToList method error: " + e);
            }
        }
        return list;
    }

    public static Map<String, Object> jsonToMap(String json) {
        Map<String, Object> maps = null;
        if (StringUtils.isNotEmpty(json)) {
            try {
                if (json.startsWith("\"{")) {
                    json = json.replace("\"{", "{").replace("}\"", "}").replace("\\\"", "\"");
                }
                maps = JSON_MAPPER.readValue(json, Map.class);
            } catch (Exception e) {
                throw new RuntimeException("jsonToMap method error: " + e);
            }
        }
        return maps;
    }

    public static Map<String, Object> jsonToMap(Object name) {
        return null;
    }

//    public static String xmlToJson()throws JSONException, IOException {
//        InputStream in = JsonUtils.class.getResourceAsStream("");
//        String xml = IOUtils.toString(in);
//        JSONObject xmlJsonObj = XML.toJSONObject(xml);
//        return xmlJsonObj.toString();
//    }

}