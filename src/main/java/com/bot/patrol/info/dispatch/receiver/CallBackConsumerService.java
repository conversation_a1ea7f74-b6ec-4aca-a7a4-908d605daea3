package com.bot.patrol.info.dispatch.receiver;

import com.alibaba.fastjson.JSONObject;
import com.bot.patrol.info.dispatch.entity.TaskInfo;
import com.bot.patrol.info.dispatch.infrastructure.constants.CommonResult;
import com.bot.patrol.info.dispatch.infrastructure.constants.EntityTypeEnum;
import com.bot.patrol.info.dispatch.infrastructure.utils.OkHttpUtils;
import com.bot.patrol.info.dispatch.infrastructure.utils.TokenBucketUtils;
import com.bot.patrol.info.dispatch.model.ESModel;
import com.bot.patrol.info.dispatch.model.ESModelDetail;
import com.bot.patrol.info.dispatch.model.IdUrlModel;
import com.bot.patrol.info.dispatch.model.Position;
import com.bot.patrol.info.dispatch.model.dto.PushDateDTO;
import com.bot.patrol.info.dispatch.model.req.CallBackReq;
import com.bot.patrol.info.dispatch.model.req.TaskInfoSearchReq;
import com.bot.patrol.info.dispatch.service.AsyncTask;
import com.bot.patrol.info.dispatch.service.HttpApiService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *消费回调接口的消息
 */

@Component
@Slf4j
public class CallBackConsumerService {
    @Autowired
    public HttpApiService httpApiService;
    @Value("${internalSystemAddress}")
    private String internalSystemAddress;
    @Autowired
    private AsyncTask asyncTask;
    @Autowired
    private TokenBucketUtils tokenBucketUtils;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Value("${customs.props.customer-direct-fail-callback}")
    private String directFcb;
    @Value("${customs.props.customer-key-fail-callback}")
    private String keyFcb;


    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = "${customs.props.customer-rabbitmq-queue}", autoDelete = "false"),
                    exchange = @Exchange(value = "${customs.props.customer-rabbitmq-direct}", durable = "true", type = ExchangeTypes.DIRECT),
                    key = "${customs.props.customer-rabbitmq-key}"
            ), concurrency = "50", ackMode = "MANUAL"
    )
    @RabbitHandler
    public void consumeMessage(Message message, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        String str = new String(message.getBody(), "utf-8");
        try {
            /**
             * todo
             * 需要根据账号查询所属任务，多次推送
             */
            CallBackReq callBackReq = JSONObject.parseObject(str, CallBackReq.class);
            TaskInfoSearchReq taskInfoSearchReq = new TaskInfoSearchReq();
            taskInfoSearchReq.setAppUrl(callBackReq.getSid());
            taskInfoSearchReq.setAppType(EntityTypeEnum.AppTypeEnum.WCAC.getCode());
            //到数据中心查询任务信息集合
            CommonResult commonResult = httpApiService.listTaskByOrderUrl(taskInfoSearchReq);
            log.info("查询所属任务"+commonResult);
            List<TaskInfo> list = new ArrayList<>();
            if (200 == commonResult.getCode()) {
                if (ObjectUtils.isNotEmpty(commonResult.getData())) {
                    list = (List<TaskInfo>) commonResult.getData();
                }
            }
            //Boolean ack=true;
            for (int i = 0; i < list.size(); i++) {
                TaskInfo taskInfo = JSONObject.parseObject(JSONObject.toJSONString(list.get(i)), TaskInfo.class);
                System.out.println("任务数据" + JSONObject.toJSONString(taskInfo));
                //巡查数据返回值
                ESModel esModel = new ESModel();
                esModel.setProjectId(taskInfo.getTiPiSid());
//                esModel.setCompanyId(taskInfo.getTiCpiSid());
                esModel.setAppId(taskInfo.getTiExternalId());
                esModel.setDataId(callBackReq.getId()+taskInfo.getSid());
                esModel.setCaseId(taskInfo.getTiCrawlerId());
                esModel.setTaskId(taskInfo.getTiExternalId());
                esModel.setHitUrl(callBackReq.getUrl());
                esModel.setScreenTime(callBackReq.getCreatTime());
                esModel.setEventId(null);
                esModel.setSearchWordId(null);
                esModel.setModuleName(null);
                esModel.setImgList(callBackReq.getImgUrls());
                esModel.setId(taskInfo.getSid());
                esModel.setTypeCode(taskInfo.getTiType());
                esModel.setCallBackUrl(taskInfo.getTiExternalCallBackUrl());
                esModel.setComments(callBackReq.getCommentNums());
                esModel.setForwardingNumber("0");
                esModel.setThumbUpFor(callBackReq.getLikeNums());
                esModel.setReadTheNumber(callBackReq.getReadNums());
                esModel.setLookingAtTheNumber(callBackReq.getLookInNums());
                esModel.setTitle(callBackReq.getTitle());
//                esModel.setAttachmentList(new ArrayList<>());
//                esModel.setImgList(callBackReq.getImgUrls());
//                esModel.setVideoList(callBackReq.getVideoUrls());
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(callBackReq));
                jsonObject.remove("title");
                jsonObject.remove("hitUrl");
                jsonObject.remove("imgList");
//                jsonObject.remove("videoList");
                Map<String, Object> jsonMap = JSONObject.toJavaObject(jsonObject, Map.class);
                //巡查数据返回值明细
                List<ESModelDetail> esModelDetails = new ArrayList<>();
                StringBuffer stringBuffer = new StringBuffer();
                for (String key : jsonMap.keySet()) {
                    Object value = jsonMap.get(key);
                    ESModelDetail esModelDetail = new ESModelDetail();
                    esModelDetail.setWord(value.toString());

                    Position position = new Position();
                    esModelDetail.setPosition(position);
                    esModelDetails.add(esModelDetail);
                    stringBuffer.append(value);
                }
                esModel.setText(callBackReq.getContentByText());
                esModel.setEsModelDetailList(esModelDetails);

                List<IdUrlModel> idUrlModels = new ArrayList<>();
                IdUrlModel idUrlModel = new IdUrlModel();
                idUrlModel.setId(esModel.getDataId());
                idUrlModel.setUrl(esModel.getHitUrl());
                idUrlModels.add(idUrlModel);

                String url = taskInfo.getTiExternalCallBackUrl();
                String content = JSONObject.toJSONString(esModel);
                String datas = null;
                boolean defaultUrl = false;
                if (StringUtils.isNotEmpty(url)) {
                    //调用回调pushData：mq消费
                    //发送至内容监测-回调接口
                    //String datas = OkHttpUtils.httpPostJson(taskInfo.getTiExternalCallBackUrl(), JSONObject.toJSONString(esModel));
                    datas = tokenBucketUtils.throttlingCallBack(url, content);
                } else {
                    defaultUrl = true;
                    url = internalSystemAddress + "api/third/patrolAudit";
                    datas = OkHttpUtils.httpPostJson(url, content);
                }
                if (StringUtils.isEmpty(datas) || (!datas.contains("\"code\":200") && !datas.contains("\"code\":500200") && !datas.contains("\"code\":500203"))) {
                    //发送至mq
                    PushDateDTO pushDateDTO = new PushDateDTO();
                    pushDateDTO.setUrl(url);
                    pushDateDTO.setContent(content);
                    pushDateDTO.setIdUrlModels(idUrlModels);
                    pushDateDTO.setDefaultUrl(defaultUrl);
                    rabbitTemplate.convertAndSend(directFcb, keyFcb, JSONObject.toJSONString(pushDateDTO));
                    log.warn("推送巡查数据失败：{}" , datas);
                } else {
                    log.info("推送巡查数据成功：{}" , content);
                    //asyncTask.execTaskA(idUrlModels);
                }
            }

            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            channel.basicNack(deliveryTag, false, true);
            e.printStackTrace();
        }
        log.info("收到的消息:{}", str);
    }

}

