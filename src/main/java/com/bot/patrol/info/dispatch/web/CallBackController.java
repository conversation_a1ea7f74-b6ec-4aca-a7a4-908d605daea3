package com.bot.patrol.info.dispatch.web;

import com.alibaba.fastjson.JSONObject;
import com.bot.patrol.info.dispatch.infrastructure.utils.ElasticsearchTemplate;
import com.bot.patrol.info.dispatch.model.req.CallBackMultipleReq;
import com.bot.patrol.info.dispatch.model.req.CallBackReq;
import com.bot.patrol.info.dispatch.service.AsyncTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/callBack")
public class CallBackController {
    @Value("${callbackKey}")
    private String key;
    @Autowired
    private ElasticsearchTemplate elasticsearchTemplate;
    @Autowired
    private AsyncTask asyncTask;
    @Autowired
    RabbitTemplate rabbitTemplate;
    @Value("${elasticsearch.index-name}")
    private String indexName;
    @Value("${customs.props.customer-rabbitmq-direct}")
    private String rabbitmqDirect;
    @Value("${customs.props.customer-rabbitmq-key}")
    private String rabbitmqKey;

    @Value("${convertPdfUrl}")
    private String convertPdfUrl;

    /**
     * 接收数据（已有：清博调用）
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/receiveData")
    public String add(@RequestBody @Valid CallBackMultipleReq request) {
        if (key.equals(request.getKey())) {
            try {
                if (!elasticsearchTemplate.existsIndex(indexName)) {
                    elasticsearchTemplate.createIndex(indexName, null, null);
                }
                List<CallBackReq> callBackReqList = request.getCallBackReqList().stream().filter(b -> StringUtils.isEmpty(b.getSid())).collect(Collectors.toList());
                if (callBackReqList.size() > 0) {
                    return "FAIL";
                }
                for (int i = 0; i < request.getCallBackReqList().size(); i++) {
                    //回调数据、文章
                    CallBackReq callBackReq1 = request.getCallBackReqList().get(i);
                    //根据文章唯一标识到es查询文章
                    CallBackReq callBackReq = elasticsearchTemplate.getById(indexName, callBackReq1.getId(), CallBackReq.class);
                    if (ObjectUtils.isNotEmpty(callBackReq)) {
                        //System.out.println("获取到的数据" + JSONObject.toJSONString(callBackReq));
                        //System.out.println("更新数据");
                        log.info("更新数据"+callBackReq1.getId());
                    } else {
                        //System.out.println("新增数据");
                        log.info("新增数据"+callBackReq1.getId());
                    }
                    elasticsearchTemplate.insert(indexName, "id", callBackReq1);
                    rabbitTemplate.convertAndSend(rabbitmqDirect, rabbitmqKey, JSONObject.toJSONString(callBackReq1));
//                    IdUrlModel idUrlModel = new IdUrlModel();
//                    idUrlModel.setId(callBackReq1.getId());
//                    idUrlModel.setUrl(callBackReq1.getUrl());
//                    idUrlModels.add(idUrlModel);
                }
//                if (idUrlModels.size() > 0) {
////                    String postJson = OkHttpUtils.httpPostJson(convertPdfUrl, JSONObject.toJSONString(idUrlModels));
////                    System.out.println("请求html转换返回数据:" + postJson);
//                    asyncTask.execTaskA(idUrlModels);
//                }


            } catch (Exception e) {
                log.error("接收数据错误：" + e.getMessage());
                e.printStackTrace();
                return "FAIL";
            }
            return "SUCCESS";
        } else {
            return "FAIL";
        }
    }




}
