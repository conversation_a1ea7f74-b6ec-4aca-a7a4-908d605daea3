package com.bot.patrol.info.dispatch.entity.ext;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ActuatorInfo{

    // id
    private String sid;

    // 所属实体
    private String aciCpiSid;

    // 队列标识
    private String aciQueueFlag;

    // 类型
    private Integer aciType;

    // 执行器访问类型
    private Integer aciConnType;

    // 版本
    private String aciVersion;

    // 名称
    private String aciName;

    // 执行器地址
    private String aciIp;

    // 执行器访问端口
    private Integer aciPort;

    // 执行器设备标识
    private String aciDevice;

    // 执行器设备网络标识
    private String aciDeviceWifi;

    // 系统类型
    private String aciSystem;

    // 系统架构
    private String aciArch;

    // 系统版本
    private String aciSystemVersion;

    // cpu信息
    private String aciCpu;

    // 内存信息
    private String aciRam;

    // 0-空闲/1-繁忙/2-等待/3-阻塞/4-异常/5-停止/6-禁用
    private Integer aciStatus;

    // 软件列表
    private String aciSoftList;

    // 测试用执行器 0-否 1-是
    private Integer aciIsTest;

    // 操作者
    private String aciOperator;

    // 创建时间
    private LocalDateTime aciCreateTime;

    // 更新时间
    private LocalDateTime aciUpdateTime;

    // 扩展字段
    private String ext;

    //执行器服务标识
    private String aciServiceFlag;

    //执行器服务名称
    private String aciServiceName;

    // 是否禁用 0-否 1-是
    private Integer aciIsStop;


}

