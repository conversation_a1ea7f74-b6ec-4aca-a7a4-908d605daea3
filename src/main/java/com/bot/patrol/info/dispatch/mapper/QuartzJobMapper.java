package com.bot.patrol.info.dispatch.mapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bot.patrol.info.dispatch.model.req.QuartzJobSearchReq;
import com.bot.patrol.info.dispatch.entity.QuartzJob;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
* <AUTHOR>
* @date 2022-03-03
*/

@Mapper
@Repository
public interface QuartzJobMapper extends BaseMapper<QuartzJob>  {
    default List<QuartzJob> selectList(QuartzJobSearchReq quartzJobSearchReq) {
        return selectList(new LambdaQueryWrapper<QuartzJob>()
            .eq(ObjectUtils.isNotEmpty(quartzJobSearchReq.getId()), QuartzJob::getId,quartzJobSearchReq.getId())
            .eq(StringUtils.isNotEmpty(quartzJobSearchReq.getBeanName()), QuartzJob::getBeanName,quartzJobSearchReq.getBeanName())
            .eq(StringUtils.isNotEmpty(quartzJobSearchReq.getCronExpression()), QuartzJob::getCronExpression,quartzJobSearchReq.getCronExpression())
            .eq(StringUtils.isNotEmpty(quartzJobSearchReq.getJobName()), QuartzJob::getJobName,quartzJobSearchReq.getJobName())
            .eq(StringUtils.isNotEmpty(quartzJobSearchReq.getMethodName()), QuartzJob::getMethodName,quartzJobSearchReq.getMethodName())
            .eq(StringUtils.isNotEmpty(quartzJobSearchReq.getParams()), QuartzJob::getParams,quartzJobSearchReq.getParams())
            .eq(StringUtils.isNotEmpty(quartzJobSearchReq.getRemark()), QuartzJob::getRemark,quartzJobSearchReq.getRemark())
            .eq(StringUtils.isNotEmpty(quartzJobSearchReq.getCertType()), QuartzJob::getCertType,quartzJobSearchReq.getCertType())
            .eq(StringUtils.isNotEmpty(quartzJobSearchReq.getTaskType()), QuartzJob::getTaskType,quartzJobSearchReq.getTaskType())
);
    }
    default List<QuartzJob> selectNoPushedList(){
        return selectList(new LambdaQueryWrapper<QuartzJob>()
                .eq(QuartzJob::getIsPush, 0)
                .eq(QuartzJob::getIsUsing, true));
    }
    default QuartzJob selectNoPushedByJobName(String jobName){
        return selectOne(new LambdaQueryWrapper<QuartzJob>()
                .eq(QuartzJob::getIsPush, 1)
                .eq(QuartzJob::getIsUsing, false)
                .eq(QuartzJob::getJobName, jobName));
    }

    default QuartzJob selectByJobName(String taskId){
        return selectOne(new LambdaQueryWrapper<QuartzJob>()
                .eq(QuartzJob::getJobName, taskId));
    }
}
