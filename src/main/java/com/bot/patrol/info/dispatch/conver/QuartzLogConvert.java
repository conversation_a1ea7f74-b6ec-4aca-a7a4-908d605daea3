package com.bot.patrol.info.dispatch.conver;


import com.bot.patrol.info.dispatch.entity.QuartzLog;
import com.bot.patrol.info.dispatch.model.req.QuartzLogAddReq;
import com.bot.patrol.info.dispatch.model.req.QuartzLogUpdateReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface QuartzLogConvert {
QuartzLogConvert INSTANCE = Mappers.getMapper(QuartzLogConvert.class);

@Mappings({})
QuartzLog convert(QuartzLogAddReq request);

@Mappings({})
QuartzLog convert(QuartzLogUpdateReq request);


}