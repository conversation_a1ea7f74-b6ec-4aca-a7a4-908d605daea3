package com.bot.patrol.info.dispatch.infrastructure.utils;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.bot.patrol.info.dispatch.entity.QuartzJob;
import com.bot.patrol.info.dispatch.entity.QuartzLog;
import com.bot.patrol.info.dispatch.manager.QuartzManage;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**工作任务：业务代码
 * 参考人人开源，https://gitee.com/renrenio/renren-security
 * <AUTHOR> @date 2019-01-07
 */
@Async
public class ExecutionJob extends QuartzJobBean {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    protected void executeInternal(JobExecutionContext context) {
        System.out.println("执行数据"+JSONObject.toJSONString(context.getMergedJobDataMap().get(QuartzJob.JOB_KEY)));
        String quartzJobString = JSONObject.toJSONString(context.getMergedJobDataMap().get(QuartzJob.JOB_KEY));
        QuartzJob quartzJob=new QuartzJob();
        if (StringUtils.isNotEmpty(quartzJobString)){
            Object parse1 = JSON.parse(quartzJobString);
            String s = parse1.toString();
            quartzJob=JSONObject.parseObject(s,QuartzJob.class);
        }
        // 获取spring bean
        //QuartzManage quartzManage = SpringContextHolder.getBean("quartzManage");

        QuartzLog log = new QuartzLog();

        long startTime = System.currentTimeMillis();

        if(StringUtils.isNotEmpty(quartzJob.getJobName())){
            try {
                // 执行任务
                logger.info("任务准备执行，任务名称：{}", quartzJob.getJobName());
                QuartzRunnable task = new QuartzRunnable( quartzJob.getBeanName(),quartzJob.getMethodName(),
                        quartzJob.getParams());
                String result = task.run();
                if(StringUtils.isNotEmpty(result)){
                    JSONObject jsonObject=null;
                    try {
                        System.out.println(result);
                        jsonObject= JSONObject.parseObject(result);
                        if(jsonObject.getBoolean("success")){
                            long times = System.currentTimeMillis() - startTime;
                            log.setTime(times);

                        }else {
                            //失败
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        //失败
                    }

                }else{
                }

            } catch (Exception e) {
                logger.error("任务执行失败，任务名称：{}" + quartzJob.getJobName(), e);
            } finally {
                System.out.println(JSONObject.toJSONString(log));
            }
        }else{
            //失败
        }

    }


}
