package com.bot.patrol.info.dispatch.infrastructure.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SourceEnum {

    SHUT_DOWN("C", "客户端"),
    TRIAL("M", "管理端"),
    PAY("S", "控制中台");

    private final String code;
    private final String value;
    public static String getValue(String code) {
        SourceEnum[] sourceEnums = values();
        for (SourceEnum sourceEnum : sourceEnums) {
            if (sourceEnum.value.equals(code)) {
                return sourceEnum.getCode();
            }
        }
        return null;
    }
}