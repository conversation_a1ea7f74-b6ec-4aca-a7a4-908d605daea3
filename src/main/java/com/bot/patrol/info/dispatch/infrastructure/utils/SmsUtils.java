package com.bot.patrol.info.dispatch.infrastructure.utils;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.bot.patrol.info.dispatch.infrastructure.constants.AppConstant;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;

@Slf4j
public class SmsUtils {

    private final static String accessKeyId = "LTAI4Fxn7yswqQq2sMUZKd9G";
    private final static String accessKeySecret = "******************************";

    private final static IAcsClient acsClient;

    static {
        IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accessKeySecret);
        acsClient = new DefaultAcsClient(profile);
    }

    public static Boolean sendVerifyCode(String phoneNumber, Integer verifyCode) {
        SendSmsRequest request = new SendSmsRequest();
        request.setPhoneNumbers(phoneNumber);
        request.setSignName("博特智能审核");
        request.setTemplateCode("SMS_183185290");
        request.setTemplateParam(JsonUtils.objectToJson(Collections.singletonMap("code", verifyCode)));
        try {
            SendSmsResponse sendSmsResponse = acsClient.getAcsResponse(request);
            log.info("手机号:{},验证码:{},发送状态:{},信息:{}", phoneNumber, verifyCode, sendSmsResponse.getCode(), sendSmsResponse.getMessage());
            if (AppConstant.SMS_SUCCESS.equals(sendSmsResponse.getCode())) {
                return Boolean.TRUE;
            }
        } catch (ClientException e) {
            log.error("手机号:{},验证码:{}发送失败", phoneNumber, verifyCode, e);
        }
        return Boolean.FALSE;
    }
}