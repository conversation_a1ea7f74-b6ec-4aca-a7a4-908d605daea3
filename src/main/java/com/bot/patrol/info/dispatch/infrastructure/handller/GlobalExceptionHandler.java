package com.bot.patrol.info.dispatch.infrastructure.handller;

import com.bot.patrol.info.dispatch.infrastructure.constants.CommonResult;
import com.bot.patrol.info.dispatch.infrastructure.constants.ErrorCodeEnum;
import com.bot.patrol.info.dispatch.infrastructure.exception.UnLoggedException;
import com.bot.patrol.info.dispatch.infrastructure.exception.UnauthenticatedException;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(value = UnLoggedException.class)
    public CommonResult unLoggedException(UnLoggedException ex) {
        return CommonResult.error(ErrorCodeEnum.UN_LOGGED);
    }

    @ExceptionHandler(value = UnauthenticatedException.class)
    public CommonResult unauthenticatedException(UnauthenticatedException ex) {
        return CommonResult.error(ErrorCodeEnum.UN_AUTHENTICATED);
    }

    @ExceptionHandler(value = NoHandlerFoundException.class)
    public CommonResult noHandlerFoundException(NoHandlerFoundException ex) {
        return CommonResult.error(HttpStatus.SC_NOT_FOUND, ex.getMessage());
    }

    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
    public CommonResult httpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException ex) {
        return CommonResult.error(HttpStatus.SC_FORBIDDEN, ex.getMessage());
    }

    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    public CommonResult httpMessageNotReadableExceptionHandler(HttpMessageNotReadableException ex) {
        String message = ex.getMessage();
        if (StringUtils.isNotBlank(message) && message.contains(":")) {
            message = message.substring(0, message.indexOf(":"));
        }
        return CommonResult.error(ErrorCodeEnum.MISSING_PARAM_ERROR.getCode(), message);
    }

    @ExceptionHandler(value = MissingServletRequestParameterException.class)
    public CommonResult missingServletRequestParameterExceptionHandler(MissingServletRequestParameterException ex) {
        return CommonResult.error(ErrorCodeEnum.MISSING_PARAM_ERROR.getCode(), ex.getMessage());
    }

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public CommonResult methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException ex) {
        FieldError fieldError = ex.getBindingResult().getFieldErrors().get(0);
        String field = fieldError.getField();
        String defaultMessage = fieldError.getDefaultMessage();
        return CommonResult.error(ErrorCodeEnum.MISSING_PARAM_ERROR.getCode(), new StringBuilder().append(field).append(defaultMessage).toString());
    }
}